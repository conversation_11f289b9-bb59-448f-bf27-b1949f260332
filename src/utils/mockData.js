import dayjs from 'dayjs'

// 模拟用户数据
export const mockUsers = [
  {
    id: '1',
    name: '张三',
    phone: '13800138001',
    type: 'teacher',
    department: '计算机学院'
  },
  {
    id: '2',
    name: '李四',
    phone: '13800138002',
    type: 'student',
    department: '软件工程专业'
  },
  {
    id: '3',
    name: '王五',
    phone: '13800138003',
    type: 'teacher',
    department: '信息工程学院'
  }
]

// 模拟预约数据
export const mockReservations = [
  {
    id: '1',
    date: dayjs().add(1, 'day').format('YYYY-MM-DD'),
    busId: 'wd1',
    busTime: '06:30',
    busRoute: '滨江至临安',
    station: '滨江校区',
    phone: '13800138001',
    userName: '张三',
    userType: 'teacher',
    userDepartment: '计算机学院',
    status: 'confirmed',
    createTime: dayjs().subtract(1, 'hour').format('YYYY-MM-DD HH:mm:ss')
  },
  {
    id: '2',
    date: dayjs().format('YYYY-MM-DD'),
    busId: 'wd4',
    busTime: '12:10',
    busRoute: '临安至滨江',
    station: '临安校区',
    phone: '13800138002',
    userName: '李四',
    userType: 'student',
    userDepartment: '软件工程专业',
    status: 'completed',
    createTime: dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'),
    checkInTime: dayjs().subtract(30, 'minute').format('YYYY-MM-DD HH:mm:ss')
  },
  {
    id: '3',
    date: dayjs().add(2, 'day').format('YYYY-MM-DD'),
    busId: 'wd3',
    busTime: '16:20',
    busRoute: '滨江至临安',
    station: '汽车西站',
    phone: '13800138003',
    userName: '王五',
    userType: 'teacher',
    userDepartment: '信息工程学院',
    status: 'confirmed',
    createTime: dayjs().subtract(30, 'minute').format('YYYY-MM-DD HH:mm:ss')
  }
]

// 模拟爽约记录
export const mockNoShowRecords = [
  {
    id: '1',
    reservationId: '10',
    date: dayjs().subtract(3, 'day').format('YYYY-MM-DD'),
    busId: 'wd1',
    recordTime: dayjs().subtract(3, 'day').format('YYYY-MM-DD HH:mm:ss')
  },
  {
    id: '2',
    reservationId: '11',
    date: dayjs().subtract(5, 'day').format('YYYY-MM-DD'),
    busId: 'wd2',
    recordTime: dayjs().subtract(5, 'day').format('YYYY-MM-DD HH:mm:ss')
  }
]

// 初始化演示数据的函数
export const initMockData = (busStore) => {
  // 设置用户信息
  if (!busStore.userInfo.name) {
    busStore.userInfo = mockUsers[0]
  }
  
  // 添加预约记录
  if (busStore.myReservations.length === 0) {
    mockReservations.forEach(reservation => {
      busStore.myReservations.push(reservation)
    })
  }
  
  // 添加爽约记录
  if (busStore.noShowRecords.length === 0) {
    mockNoShowRecords.forEach(record => {
      busStore.noShowRecords.push(record)
    })
  }
}

// 生成更多测试数据的函数
export const generateTestData = (busStore) => {
  const testReservations = []
  const today = dayjs()
  
  // 生成未来7天的测试预约数据
  for (let i = 1; i <= 7; i++) {
    const date = today.add(i, 'day')
    const schedules = busStore.getScheduleByDate(date.format('YYYY-MM-DD'))
    
    schedules.forEach((schedule, index) => {
      if (Math.random() > 0.5) { // 50%概率生成预约
        testReservations.push({
          id: `test_${i}_${index}`,
          date: date.format('YYYY-MM-DD'),
          busId: schedule.id,
          busTime: schedule.time,
          busRoute: schedule.route,
          station: schedule.stations[Math.floor(Math.random() * schedule.stations.length)],
          phone: `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
          userName: `测试用户${i}${index}`,
          userType: Math.random() > 0.5 ? 'teacher' : 'student',
          userDepartment: ['计算机学院', '信息工程学院', '软件工程专业', '电子工程学院'][Math.floor(Math.random() * 4)],
          status: 'confirmed', // 所有预约直接确认
          createTime: today.subtract(Math.floor(Math.random() * 24), 'hour').format('YYYY-MM-DD HH:mm:ss')
        })
      }
    })
  }
  
  // 添加到store
  testReservations.forEach(reservation => {
    busStore.myReservations.push(reservation)
  })
  
  console.log(`生成了 ${testReservations.length} 条测试预约数据`)
}
