import * as dd from 'dingtalk-jsapi'
import { dingtalkAuthApi } from '../api/dingtalkAuth'
import { setToken } from './auth'
import { useBusStore } from '../stores/bus'

/**
 * 钉钉工具类
 */
export class DingTalkUtils {
  constructor() {
    this.isReady = false
    // this.init()
  }

  /**
   * 初始化钉钉JS-SDK
   */
  async init() {
    try {
      // 检查是否在钉钉环境中
      if (!this.isDingTalkEnv()) {
        console.warn('当前不在钉钉环境中')
        // 在开发环境下引入vconsole
        return false
      }
      // 等待钉钉SDK准备就绪
      await dd.ready()
      this.isReady = true
      console.log('钉钉SDK初始化成功')
      return true
    } catch (error) {
      console.error('钉钉SDK初始化失败:', error)
      return false
    }
  }

  /**
   * 检查是否在钉钉环境中
   */
  isDingTalkEnv() {
    let userAgent = navigator.userAgent;
    return userAgent.indexOf("DingTalk") !== -1
  }

  /**
   * 获取用户信息 - 实现无感免登
   * @returns {Promise<Object>} 用户信息对象
   * @throws {Error} 认证失败时抛出异常
   */
  async getUserInfo() {
    if (!this.isReady) {
      throw new Error('钉钉SDK未初始化')
    }
    try {
      // 配置免登
      const _config = await this.getConfig()
      // 获取免登授权码
      const { code } = await dd.runtime.permission.requestAuthCode({
        corpId: _config.corpId
      })
      console.log('获取到钉钉授权码:', code)
      // 调用后端接口，通过authCode获取用户信息和token
      const authResult = await dingtalkAuthApi.dingAuth(code)
      console.log('钉钉认证结果:', authResult)
      // 验证返回结果格式
      if (!authResult || !authResult.token || !authResult.user || !authResult.role) {
        throw new Error('钉钉认证返回数据格式不正确')
      }
      // 存储token
      setToken(authResult.token)

      // 更新用户信息到store
      const busStore = useBusStore()
      const userData = authResult.user.user || authResult.user
      busStore.userInfo = authResult.user

      console.log('用户信息更新成功:', busStore.userInfo)
      return busStore.userInfo
    } catch (error) {
      console.error('钉钉免登认证失败:', error)
      throw error
    }
  }

  /**
   * 获取免登配置
   * @returns {Promise<Object>} 钉钉配置信息
   * @throws {Error} 配置获取失败时抛出异常
   */
  async getConfig() {
    try {
      const _config = await dingtalkAuthApi.conf()
      // 验证配置结果
      if (!_config || typeof _config !== 'object') {
        throw new Error('获取的钉钉配置格式无效')
      }
      return _config
    } catch (error) {
      throw new Error(`钉钉免登配置获取失败: ${error.message || '未知错误'}`)
    }
  }

  /**
   * 执行自动登录
   * @returns {Promise<boolean>} 登录是否成功
   */
  async autoLogin() {
    try {
      // 检查是否在钉钉环境
      if (!this.isDingTalkEnv()) {
        console.log('不在钉钉环境，跳过自动登录')
        return false
      }

      // 初始化SDK
      const initialized = await this.init()
      if (!initialized) {
        console.error('SDK初始化失败，无法自动登录')
        return false
      }
      // 执行免登认证
      await this.getUserInfo()
      console.log('钉钉自动登录成功')
      return true
    } catch (error) {
      console.error('钉钉自动登录失败:', error)
      return false
    }
  }

  /**
   * 扫描二维码
   */
  async scanQRCode() {
    if (!this.isReady) {
      throw new Error('钉钉SDK未初始化')
    }

    try {
      const result = await dd.biz.util.scan({
        type: 'qr' // 扫描二维码
      })

      return {
        success: true,
        text: result.text, // 扫描结果
        format: result.format // 格式类型
      }
    } catch (error) {
      console.error('扫码失败:', error)
      throw error
    }
  }

  /**
   * 显示成功提示
   */
  async showSuccessToast(message) {
    if (!this.isReady) {
      console.log(message)
      return
    }

    try {
      await dd.device.notification.toast({
        icon: 'success',
        text: message,
        duration: 2
      })
    } catch (error) {
      console.error('显示提示失败:', error)
    }
  }

  /**
   * 显示错误提示
   */
  async showErrorToast(message) {
    if (!this.isReady) {
      console.error(message)
      return
    }

    try {
      await dd.device.notification.toast({
        icon: 'error',
        text: message,
        duration: 3
      })
    } catch (error) {
      console.error('显示提示失败:', error)
    }
  }

  /**
   * 显示确认对话框
   */
  async showConfirm(title, message) {
    if (!this.isReady) {
      return confirm(`${title}\n${message}`)
    }

    try {
      const result = await dd.device.notification.confirm({
        message: message,
        title: title,
        buttonLabels: ['取消', '确定']
      })
      return result.buttonIndex === 1
    } catch (error) {
      console.error('显示确认对话框失败:', error)
      return false
    }
  }

  /**
   * 获取地理位置
   */
  async getLocation() {
    if (!this.isReady) {
      throw new Error('钉钉SDK未初始化')
    }

    try {
      const result = await dd.device.geolocation.get({
        targetAccuracy: 200,
        coordinate: 1, // 返回国测局坐标
        withReGeocode: true,
        useCache: true
      })

      return {
        latitude: result.latitude,
        longitude: result.longitude,
        accuracy: result.accuracy,
        address: result.address,
        province: result.province,
        city: result.city,
        district: result.district
      }
    } catch (error) {
      console.error('获取位置失败:', error)
      throw error
    }
  }

  /**
   * 设置导航栏
   */
  async setNavigation(options = {}) {
    if (!this.isReady) {
      return
    }

    try {
      await dd.biz.navigation.setTitle({
        title: options.title || '校车预约'
      })

      if (options.showBack !== false) {
        await dd.biz.navigation.setLeft({
          show: true,
          control: true,
          text: options.backText || '返回'
        })
      }

      if (options.rightButton) {
        await dd.biz.navigation.setRight({
          show: true,
          control: true,
          text: options.rightButton.text || '更多'
        })
      }
    } catch (error) {
      console.error('设置导航栏失败:', error)
    }
  }

  /**
   * 关闭当前页面
   */
  async closePage() {
    if (!this.isReady) {
      window.history.back()
      return
    }

    try {
      await dd.biz.navigation.close()
    } catch (error) {
      console.error('关闭页面失败:', error)
      window.history.back()
    }
  }

  /**
   * 分享内容
   */
  async share(options) {
    if (!this.isReady) {
      throw new Error('钉钉SDK未初始化')
    }

    try {
      await dd.biz.util.share({
        type: options.type || 0, // 0:链接 1:文本
        url: options.url || window.location.href,
        title: options.title || '校车预约',
        content: options.content || '便捷的校车预约服务',
        image: options.image || ''
      })
    } catch (error) {
      console.error('分享失败:', error)
      throw error
    }
  }
}

// 创建全局实例
export const dingTalk = new DingTalkUtils()

// 默认导出
export default dingTalk