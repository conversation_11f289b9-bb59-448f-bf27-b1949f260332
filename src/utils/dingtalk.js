import * as dd from 'dingtalk-jsapi'

/**
 * 钉钉工具类
 */
export class DingTalkUtils {
  constructor() {
    this.isReady = false
    this.init()
  }

  /**
   * 初始化钉钉JS-SDK
   */
  async init() {
    try {
      // 检查是否在钉钉环境中
      if (!dd.env.platform) {
        console.warn('当前不在钉钉环境中')
        return false
      }

      // 等待钉钉SDK准备就绪
      await dd.ready()
      this.isReady = true
      console.log('钉钉SDK初始化成功')
      return true
    } catch (error) {
      console.error('钉钉SDK初始化失败:', error)
      return false
    }
  }

  /**
   * 检查是否在钉钉环境中
   */
  isDingTalkEnv() {
    return dd.env.platform !== undefined
  }

  /**
   * 获取用户信息
   */
  async getUserInfo() {
    if (!this.isReady) {
      throw new Error('钉钉SDK未初始化')
    }

    try {
      // 获取免登授权码
      const authCode = await dd.runtime.permission.requestAuthCode({
        corpId: process.env.VUE_APP_DINGTALK_CORP_ID || 'your_corp_id'
      })

      // 这里需要调用后端接口，通过authCode获取用户信息
      // 由于是演示，我们返回模拟数据
      return {
        userid: 'demo_user_001',
        name: '张三',
        mobile: '13800138001',
        department: ['计算机学院'],
        position: '教师'
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  /**
   * 扫描二维码
   */
  async scanQRCode() {
    if (!this.isReady) {
      throw new Error('钉钉SDK未初始化')
    }

    try {
      const result = await dd.biz.util.scan({
        type: 'qr' // 扫描二维码
      })

      return {
        success: true,
        text: result.text, // 扫描结果
        format: result.format // 格式类型
      }
    } catch (error) {
      console.error('扫码失败:', error)
      throw error
    }
  }

  /**
   * 显示成功提示
   */
  async showSuccessToast(message) {
    if (!this.isReady) {
      console.log(message)
      return
    }

    try {
      await dd.device.notification.toast({
        icon: 'success',
        text: message,
        duration: 2
      })
    } catch (error) {
      console.error('显示提示失败:', error)
    }
  }

  /**
   * 显示错误提示
   */
  async showErrorToast(message) {
    if (!this.isReady) {
      console.error(message)
      return
    }

    try {
      await dd.device.notification.toast({
        icon: 'error',
        text: message,
        duration: 3
      })
    } catch (error) {
      console.error('显示提示失败:', error)
    }
  }

  /**
   * 显示确认对话框
   */
  async showConfirm(title, message) {
    if (!this.isReady) {
      return confirm(`${title}\n${message}`)
    }

    try {
      const result = await dd.device.notification.confirm({
        message: message,
        title: title,
        buttonLabels: ['取消', '确定']
      })
      return result.buttonIndex === 1
    } catch (error) {
      console.error('显示确认对话框失败:', error)
      return false
    }
  }

  /**
   * 获取地理位置
   */
  async getLocation() {
    if (!this.isReady) {
      throw new Error('钉钉SDK未初始化')
    }

    try {
      const result = await dd.device.geolocation.get({
        targetAccuracy: 200,
        coordinate: 1, // 返回国测局坐标
        withReGeocode: true,
        useCache: true
      })

      return {
        latitude: result.latitude,
        longitude: result.longitude,
        accuracy: result.accuracy,
        address: result.address,
        province: result.province,
        city: result.city,
        district: result.district
      }
    } catch (error) {
      console.error('获取位置失败:', error)
      throw error
    }
  }

  /**
   * 设置导航栏
   */
  async setNavigation(options = {}) {
    if (!this.isReady) {
      return
    }

    try {
      await dd.biz.navigation.setTitle({
        title: options.title || '校车预约'
      })

      if (options.showBack !== false) {
        await dd.biz.navigation.setLeft({
          show: true,
          control: true,
          text: options.backText || '返回'
        })
      }

      if (options.rightButton) {
        await dd.biz.navigation.setRight({
          show: true,
          control: true,
          text: options.rightButton.text || '更多'
        })
      }
    } catch (error) {
      console.error('设置导航栏失败:', error)
    }
  }

  /**
   * 关闭当前页面
   */
  async closePage() {
    if (!this.isReady) {
      window.history.back()
      return
    }

    try {
      await dd.biz.navigation.close()
    } catch (error) {
      console.error('关闭页面失败:', error)
      window.history.back()
    }
  }

  /**
   * 分享内容
   */
  async share(options) {
    if (!this.isReady) {
      throw new Error('钉钉SDK未初始化')
    }

    try {
      await dd.biz.util.share({
        type: options.type || 0, // 0:链接 1:文本
        url: options.url || window.location.href,
        title: options.title || '校车预约',
        content: options.content || '便捷的校车预约服务',
        image: options.image || ''
      })
    } catch (error) {
      console.error('分享失败:', error)
      throw error
    }
  }
}

// 创建全局实例
export const dingTalk = new DingTalkUtils()

// 默认导出
export default dingTalk
