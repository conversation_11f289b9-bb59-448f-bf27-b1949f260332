import Cookies from "js-cookie";

const TokenKey = 'EL-ADMIN-TOEKN';

export function getToken() {
  return Cookies.get(TokenKey);
}

let num = 2; //失效时间是几小时
let time = new Date(new Date().getTime() + num * 60 * 60 * 1000);
export function setToken(token, rememberMe) {
  if (rememberMe) {
    return Cookies.set(TokenKey, token, { expires: time });
  } else return Cookies.set(TokenKey, token, { expires: time });
}

export function removeToken() {
  return Cookies.remove(Token<PERSON>ey);
}
