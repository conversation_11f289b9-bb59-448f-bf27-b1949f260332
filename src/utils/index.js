import dayjs from 'dayjs'

/**
 * 格式化日期
 * @param {string|Date} date 日期
 * @param {string} format 格式化字符串
 * @returns {string} 格式化后的日期字符串
 */
export const formatDate = (date, format = 'YYYY-MM-DD') => {
  return dayjs(date).format(format)
}

/**
 * 格式化时间
 * @param {string|Date} time 时间
 * @param {string} format 格式化字符串
 * @returns {string} 格式化后的时间字符串
 */
export const formatTime = (time, format = 'HH:mm:ss') => {
  return dayjs(time).format(format)
}

/**
 * 格式化日期时间
 * @param {string|Date} datetime 日期时间
 * @param {string} format 格式化字符串
 * @returns {string} 格式化后的日期时间字符串
 */
export const formatDateTime = (datetime, format = 'YYYY-MM-DD HH:mm:ss') => {
  return dayjs(datetime).format(format)
}

/**
 * 获取友好的时间显示
 * @param {string|Date} time 时间
 * @returns {string} 友好的时间显示
 */
export const getTimeAgo = (time) => {
  const now = dayjs()
  const target = dayjs(time)
  const diff = now.diff(target, 'minute')
  
  if (diff < 1) {
    return '刚刚'
  } else if (diff < 60) {
    return `${diff}分钟前`
  } else if (diff < 1440) {
    return `${Math.floor(diff / 60)}小时前`
  } else if (diff < 10080) {
    return `${Math.floor(diff / 1440)}天前`
  } else {
    return target.format('MM-DD')
  }
}

/**
 * 检查是否为今天
 * @param {string|Date} date 日期
 * @returns {boolean} 是否为今天
 */
export const isToday = (date) => {
  return dayjs(date).isSame(dayjs(), 'day')
}

/**
 * 检查是否为明天
 * @param {string|Date} date 日期
 * @returns {boolean} 是否为明天
 */
export const isTomorrow = (date) => {
  return dayjs(date).isSame(dayjs().add(1, 'day'), 'day')
}

/**
 * 获取星期几的中文显示
 * @param {string|Date} date 日期
 * @returns {string} 星期几
 */
export const getWeekday = (date) => {
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  return weekdays[dayjs(date).day()]
}

/**
 * 验证手机号格式
 * @param {string} phone 手机号
 * @returns {boolean} 是否为有效手机号
 */
export const validatePhone = (phone) => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证邮箱格式
 * @param {string} email 邮箱
 * @returns {boolean} 是否为有效邮箱
 */
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
export const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 深拷贝对象
 * @param {any} obj 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item))
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} delay 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export const debounce = (func, delay) => {
  let timeoutId
  return function (...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} delay 延迟时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export const throttle = (func, delay) => {
  let lastCall = 0
  return function (...args) {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      return func.apply(this, args)
    }
  }
}

/**
 * 存储到本地存储
 * @param {string} key 键名
 * @param {any} value 值
 */
export const setStorage = (key, value) => {
  try {
    localStorage.setItem(key, JSON.stringify(value))
  } catch (error) {
    console.error('存储数据失败:', error)
  }
}

/**
 * 从本地存储获取数据
 * @param {string} key 键名
 * @param {any} defaultValue 默认值
 * @returns {any} 存储的值或默认值
 */
export const getStorage = (key, defaultValue = null) => {
  try {
    const item = localStorage.getItem(key)
    return item ? JSON.parse(item) : defaultValue
  } catch (error) {
    console.error('获取存储数据失败:', error)
    return defaultValue
  }
}

/**
 * 从本地存储删除数据
 * @param {string} key 键名
 */
export const removeStorage = (key) => {
  try {
    localStorage.removeItem(key)
  } catch (error) {
    console.error('删除存储数据失败:', error)
  }
}

/**
 * 清空本地存储
 */
export const clearStorage = () => {
  try {
    localStorage.clear()
  } catch (error) {
    console.error('清空存储数据失败:', error)
  }
}
