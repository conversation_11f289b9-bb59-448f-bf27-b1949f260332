import dayjs from 'dayjs'

// 模拟接口返回的班次数据
export const mockSchedulesResponse = [
  {
    id: 1,
    start_station: '滨江校区',
    end_station: '临安校区',
    time: '06:30',
    date: dayjs().format('YYYY-MM-DD'),
    station: '滨江校区,汽车西站,五常站,青山湖地铁口',
    drivers: {
      id: 1,
      name: '张师傅',
      phone: '13800138001',
      dingUserId: 'driver001',
      licensePlate: '浙A12345'
    }
  },
  {
    id: 2,
    start_station: '滨江校区',
    end_station: '临安校区',
    time: '11:50',
    date: dayjs().format('YYYY-MM-DD'),
    station: '滨江校区,汽车西站,五常站,青山湖地铁口',
    drivers: {
      id: 2,
      name: '李师傅',
      phone: '13800138002',
      dingUserId: 'driver002',
      licensePlate: '浙A67890'
    }
  },
  {
    id: 3,
    start_station: '滨江校区',
    end_station: '临安校区',
    time: '16:20',
    date: dayjs().format('YYYY-MM-DD'),
    station: '滨江校区,汽车西站,五常站,青山湖地铁口',
    drivers: {
      id: 3,
      name: '王师傅',
      phone: '13800138003',
      dingUserId: 'driver003',
      licensePlate: '浙A11111'
    }
  },
  {
    id: 4,
    start_station: '临安校区',
    end_station: '滨江校区',
    time: '12:10',
    date: dayjs().format('YYYY-MM-DD'),
    station: '临安校区,青山湖地铁口,五常站,汽车西站',
    drivers: {
      id: 3,
      name: '王师傅',
      phone: '13800138003',
      dingUserId: 'driver003',
      licensePlate: '浙A11111'
    }
  },
  {
    id: 5,
    start_station: '临安校区',
    end_station: '滨江校区',
    time: '17:00',
    date: dayjs().format('YYYY-MM-DD'),
    station: '临安校区,青山湖地铁口,五常站,汽车西站',
    drivers: {
      id: 3,
      name: '王师傅',
      phone: '13800138003',
      dingUserId: 'driver003',
      licensePlate: '浙A11111'
    }
  },
  {
    id: 6,
    start_station: '临安校区',
    end_station: '滨江校区',
    time: '21:20',
    date: dayjs().format('YYYY-MM-DD'),
    station: '临安校区,青山湖地铁口,五常站,汽车西站',
    drivers: {
      id: 3,
      name: '王师傅',
      phone: '13800138003',
      dingUserId: 'driver003',
      licensePlate: '浙A11111'
    }
  },
]

// 模拟接口返回的预约数据
export const mockReservationsResponse = [
  {
    id: 1,
    name: '张三',
    phone: '13800138001',
    dingUserId: 'user001',
    departureSpot: '滨江校区',
    busSchedules: {
      id: 1,
      start_station: '滨江校区',
      end_station: '临安校区',
      time: '06:30',
      date: dayjs().add(1, 'day').format('YYYY-MM-DD'),
      station: '滨江校区',
      drivers: {
        id: 1,
        name: '张师傅',
        phone: '13800138001',
        dingUserId: 'driver001',
        licensePlate: '浙A12345'
      }
    },
    date: dayjs().add(1, 'day').format('YYYY-MM-DD'),
    isRide: 'N',
    state: 'confirmed',
    signTime: null
  }
]

// 模拟接口返回的爽约记录数据
export const mockNoShowRecordsResponse = {
    id: 1,
    name: '张三',
    phone: '13800138001',
    num: 1,
    state: 'active',
    date: dayjs().subtract(3, 'day').format('YYYY-MM-DD'),
    reservationsList: [
      {
        id: 10,
        name: '张三',
        phone: '13800138001',
        dingUserId: 'user001',
        departureSpot: '滨江校区',
        busSchedules: {
          id: 1,
          start_station: '滨江校区',
          end_station: '临安校区',
          time: '06:30',
          date: dayjs().subtract(3, 'day').format('YYYY-MM-DD'),
          station: '滨江校区'
        },
        date: dayjs().subtract(3, 'day').format('YYYY-MM-DD'),
        isRide: 'N',
        state: 'confirmed',
        signTime: null
      }
    ]
  }

// 模拟接口返回的驾驶员数据
export const mockDriversResponse = [
  {
    id: 1,
    name: '张师傅',
    phone: '13800138001',
    dingUserId: 'driver001',
    licensePlate: '浙A12345'
  },
  {
    id: 2,
    name: '李师傅',
    phone: '13800138002',
    dingUserId: 'driver002',
    licensePlate: '浙A67890'
  },
  {
    id: 3,
    name: '王师傅',
    phone: '13800138003',
    dingUserId: 'driver003',
    licensePlate: '浙A11111'
  }
]

// 模拟接口返回的统计数据
export const mockStatisticsResponse = {
  total: 10,
  checkedIn: 8,
  cancelled: 2
}

// 模拟接口返回的爽约统计数据
export const mockNoShowStatisticsResponse = {
  total: 5,
  restricted: 1
}

// 检查是否为开发环境
export const isDevelopment = () => {
  return process.env.NODE_ENV === 'development'
}

// 模拟API延迟
export const mockDelay = (ms = 500) => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 模拟API响应
export const mockApiResponse = async (data, delay = 500) => {
  await mockDelay(delay)
  return data
}
