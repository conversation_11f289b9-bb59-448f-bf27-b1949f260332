import { useBusStore } from '../../stores/bus.js'

export default {
  inserted(el, binding) {
    const { value } = binding
    // 正确获取store实例
    const busStore = useBusStore()
    // busStore.role 格式为 [{"dataScope": "全部", "id": 2, "level": 2, "name": "教师"},{"dataScope": "全部", "id": 2943816427110400, "level": 3, "name": "校车管理员"}]
    const roles = busStore.role || []

    if (value && value instanceof Array) {
      if (value.length > 0) {
        const permissionRoles = value
        // 修复权限检查逻辑，比较角色对象的name属性
        const hasPermission = roles.some(role => {
          return permissionRoles.includes(role.name)
        })
        if (!hasPermission) {
          el.parentNode && el.parentNode.removeChild(el)
        }
      }
    } else {
      console.log(`使用方式： v-permission="['教师','校车管理员']"`)
    }
  }
}
