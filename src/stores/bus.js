import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import { noshowRecordsApi } from '../api/noshowrecords'

dayjs.extend(isBetween)

export const useBusStore = defineStore('bus', () => {
  // 用户信息
  const userInfo = ref({
    id: '',
    name: '张三',
    phone: '18300021020',
    department: '',
    role: ['user'] // user, driver, admin
  })

  // 班车时刻表配置
  const busSchedules = ref({
    weekday: [
      {
        id: 'wd1',
        time: '06:30',
        route: '滨江至临安',
        stations: ['滨江校区', '汽车西站', '五常站', '青山湖地铁口']
      },
      {
        id: 'wd2',
        time: '11:50',
        route: '滨江至临安',
        stations: ['滨江校区', '汽车西站', '五常站', '青山湖地铁口']
      },
      {
        id: 'wd3',
        time: '16:20',
        route: '滨江至临安',
        stations: ['滨江校区', '汽车西站', '五常站', '青山湖地铁口']
      },
      {
        id: 'wd4',
        time: '12:10',
        route: '临安至滨江',
        stations: ['临安校区', '青山湖地铁口', '五常站', '汽车西站']
      },
      {
        id: 'wd5',
        time: '17:00',
        route: '临安至滨江',
        stations: ['临安校区', '青山湖地铁口', '五常站', '汽车西站']
      },
      {
        id: 'wd6',
        time: '21:20',
        route: '临安至滨江',
        stations: ['临安校区', '青山湖地铁口', '五常站', '汽车西站']
      }
    ],
    weekend: [
      {
        id: 'we1',
        time: '16:20',
        route: '滨江至临安',
        stations: ['滨江校区', '汽车西站', '五常站', '青山湖地铁口']
      },
      {
        id: 'we2',
        time: '18:30',
        route: '临安至滨江',
        stations: ['临安校区', '青山湖地铁口', '五常站', '汽车西站']
      }
    ],
    vacation: [
      {
        id: 'va1',
        time: '07:30',
        route: '滨江至临安',
        stations: ['滨江校区', '汽车西站', '五常站', '青山湖地铁口']
      },
      {
        id: 'va2',
        time: '16:30',
        route: '临安至滨江',
        stations: ['临安校区', '青山湖地铁口', '五常站', '汽车西站']
      }
    ]
  })

  // 我的预约记录
  const myReservations = ref([])

  // 爽约记录
  const noShowRecords = ref([])
  const noShowRecordsLoading = ref(false)
  
  // 假期日期管理
  const summerVacations = ref([
    // 这里存储暑假日期区间
    // { year: 2023, start: '2023-07-01', end: '2023-08-31' },
    // { year: 2024, start: '2024-07-01', end: '2024-08-31' }
  ])

  const winterVacations = ref([
    // 这里存储寒假日期区间
    // { year: 2023, start: '2023-01-15', end: '2023-02-15' },
    // { year: 2024, start: '2024-01-15', end: '2024-02-15' }
  ])

  const holidays = ref([
    // 这里存储节假日日期
    // '2023-01-01', // 元旦
    // '2023-01-21', // 春节
    // '2023-01-22',
    // '2023-01-23',
  ])

  // 车辆管理
  const vehicles = ref([
    // 这里存储车辆信息
    // {
    //   id: '1',
    //   vehicleNumber: '浙A12345',
    //   vehicleModel: '宇通大巴',
    //   capacity: 45,
    //   status: 'active',
    //   notes: '正常使用'
    // }
  ])

  const drivers = ref([
    // 这里存储驾驶员信息
    // {
    //   id: '1',
    //   name: '张师傅',
    //   phone: '13800138001',
    //   licenseNumber: 'A1234567890123',
    //   status: 'active'
    // }
  ])

  // 班次安排记录
  const scheduleAssignments = ref([
    // 这里存储班次安排信息
    // {
    //   id: '1',
    //   date: '2023-10-01',
    //   scheduleId: 'wd1',
    //   vehicleId: '1',
    //   driverId: '1',
    //   notes: '国庆期间正常运行'
    // }
  ])

  // 判断给定日期是否为假期
  const isVacationDate = (date) => {
    const targetDate = dayjs(date)
    const year = targetDate.year()
    const dateStr = targetDate.format('YYYY-MM-DD')

    // 检查是否为节假日
    if (holidays.value.includes(dateStr)) {
      return true
    }

    // 检查是否在暑假期间
    for (const vacation of summerVacations.value) {
      if (vacation.year === year && 
          targetDate.isBetween(vacation.start, vacation.end, 'day', '[]')) {
        return true
      }
    }

    // 检查是否在寒假期间
    for (const vacation of winterVacations.value) {
      if (vacation.year === year && 
          targetDate.isBetween(vacation.start, vacation.end, 'day', '[]')) {
        return true
      }
    }

    return false
  }

  // 获取指定日期的班车时刻表
  const getScheduleByDate = computed(() => {
    return (date) => {
      const dayOfWeek = dayjs(date).day()
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6
      const isVacation = isVacationDate(date) // 使用假期判断函数
      
      if (isVacation) {
        return busSchedules.value.vacation
      } else if (isWeekend) {
        // 周日才有班车，周六没有
        return dayOfWeek === 0 ? busSchedules.value.weekend : []
      } else {
        return busSchedules.value.weekday
      }
    }
  })

  // 假期管理相关方法
  const addSummerVacation = (vacation) => {
    const exists = summerVacations.value.some(v => 
      v.year === vacation.year && v.start === vacation.start && v.end === vacation.end
    )
    if (!exists) {
      summerVacations.value.push(vacation)
    }
  }

  const updateSummerVacation = (index, vacation) => {
    if (index >= 0 && index < summerVacations.value.length) {
      summerVacations.value[index] = vacation
    }
  }

  const deleteSummerVacation = (index) => {
    if (index >= 0 && index < summerVacations.value.length) {
      summerVacations.value.splice(index, 1)
    }
  }

  const addWinterVacation = (vacation) => {
    const exists = winterVacations.value.some(v => 
      v.year === vacation.year && v.start === vacation.start && v.end === vacation.end
    )
    if (!exists) {
      winterVacations.value.push(vacation)
    }
  }

  const updateWinterVacation = (index, vacation) => {
    if (index >= 0 && index < winterVacations.value.length) {
      winterVacations.value[index] = vacation
    }
  }

  const deleteWinterVacation = (index) => {
    if (index >= 0 && index < winterVacations.value.length) {
      winterVacations.value.splice(index, 1)
    }
  }

  const addHoliday = (date) => {
    if (!holidays.value.includes(date)) {
      holidays.value.push(date)
    }
  }

  const removeHoliday = (date) => {
    const index = holidays.value.indexOf(date)
    if (index >= 0) {
      holidays.value.splice(index, 1)
    }
  }

  // 车辆管理相关方法
  const addVehicle = (vehicle) => {
    const exists = vehicles.value.some(v => v.vehicleNumber === vehicle.vehicleNumber)
    if (!exists) {
      vehicles.value.push(vehicle)
    }
  }

  const updateVehicle = (vehicle) => {
    const index = vehicles.value.findIndex(v => v.id === vehicle.id)
    if (index >= 0) {
      vehicles.value[index] = vehicle
    }
  }

  const deleteVehicle = (vehicleId) => {
    const index = vehicles.value.findIndex(v => v.id === vehicleId)
    if (index >= 0) {
      vehicles.value.splice(index, 1)
    }
  }

  // 驾驶员管理相关方法
  const addDriver = (driver) => {
    const exists = drivers.value.some(d => d.phone === driver.phone || d.licenseNumber === driver.licenseNumber)
    if (!exists) {
      drivers.value.push(driver)
    }
  }

  const updateDriver = (driver) => {
    const index = drivers.value.findIndex(d => d.id === driver.id)
    if (index >= 0) {
      drivers.value[index] = driver
    }
  }

  const deleteDriver = (driverId) => {
    const index = drivers.value.findIndex(d => d.id === driverId)
    if (index >= 0) {
      drivers.value.splice(index, 1)
    }
  }

  // 班次安排相关方法
  const saveScheduleAssignment = (assignment) => {
    const existingIndex = scheduleAssignments.value.findIndex(a => 
      a.date === assignment.date && a.scheduleId === assignment.scheduleId
    )
    
    const newAssignment = {
      id: existingIndex >= 0 ? scheduleAssignments.value[existingIndex].id : Date.now().toString(),
      date: assignment.date,
      scheduleId: assignment.scheduleId,
      vehicleNumber: assignment.vehicleNumber,
      driverId: assignment.driverId,
      driverName: assignment.driverName
    }
    
    if (existingIndex >= 0) {
      scheduleAssignments.value[existingIndex] = newAssignment
    } else {
      scheduleAssignments.value.push(newAssignment)
    }
  }

  // 获取指定日期和班次的安排
  const getScheduleAssignment = computed(() => {
    return (date, scheduleId) => {
      return scheduleAssignments.value.find(a => 
        a.date === date && a.scheduleId === scheduleId
      )
    }
  })

  // 获取指定日期的所有班次安排
  const getAssignmentsByDate = computed(() => {
    return (date) => {
      return scheduleAssignments.value.filter(a => a.date === date)
    }
  })

  // 检查是否可以预约指定日期
  const canReserveDate = computed(() => {
    return (date) => {
      const now = dayjs()
      const targetDate = dayjs(date)
      const cutoffTime = now.hour(18).minute(0).second(0)
      
      // 如果当前时间超过18:00，则不能预约明天，只能预约后天开始
      if (now.isAfter(cutoffTime)) {
        return targetDate.isAfter(now.add(1, 'day').startOf('day'))
      } else {
        return targetDate.isAfter(now.startOf('day'))
      }
    }
  })

  // 获取可预约的日期范围
  const getAvailableDates = computed(() => {
    const dates = []
    const now = dayjs()
    const cutoffTime = now.hour(18).minute(0).second(0)
    
    // 确定起始日期
    let startDate
    if (now.isAfter(cutoffTime)) {
      startDate = now.add(2, 'day') // 后天开始
    } else {
      startDate = now.add(1, 'day') // 明天开始
    }
    
    // 生成未来7天的可预约日期
    for (let i = 0; i < 7; i++) {
      const date = startDate.add(i, 'day')
      dates.push({
        date: date.format('YYYY-MM-DD'),
        dateText: date.format('MM月DD日'),
        dayText: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.day()],
        schedules: getScheduleByDate.value(date.format('YYYY-MM-DD'))
      })
    }
    
    return dates
  })

  // 添加预约
  const addReservation = (reservation) => {
    const newReservation = {
      id: Date.now().toString(),
      ...reservation,
      status: 'confirmed', // 直接确认，无需等待确认步骤
      createTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
    }
    myReservations.value.push(newReservation)
    return newReservation
  }

  // 取消预约
  const cancelReservation = (id) => {
    const index = myReservations.value.findIndex(r => r.id === id)
    if (index > -1) {
      myReservations.value[index].status = 'cancelled'
    }
  }

  // 签到确认
  const checkIn = (reservationId) => {
    const index = myReservations.value.findIndex(r => r.id === reservationId)
    if (index > -1) {
      myReservations.value[index].status = 'completed'
      myReservations.value[index].checkInTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    }
  }

  // 从接口获取我的爽约记录
  const recordNoShow = async () => {
    try {
      const params = {
        phone: userInfo.value.phone
      }
      const response = await noshowRecordsApi.myRecords(params)
       noShowRecords.value = response
       noShowRecords.value = {
        id: 1,
        name: '张三',
        phone: '13800138001',
        num: 1,
        state: 'active',
        date: dayjs().subtract(3, 'day').format('YYYY-MM-DD'),
        reservationsList: [
          {
            id: 10,
            name: '张三',
            phone: '13800138001',
            dingUserId: 'user001',
            departureSpot: '滨江校区',
            busSchedules: {
              id: 1,
              start_station: '滨江校区',
              end_station: '临安校区',
              time: '06:30',
              date: dayjs().subtract(3, 'day').format('YYYY-MM-DD'),
              station: '滨江校区'
            },
            date: dayjs().subtract(3, 'day').format('YYYY-MM-DD'),
            isRide: 'N',
            state: 'confirmed',
            signTime: null
          }
        ]
      }
    } catch (error) {
      console.error('获取爽约记录失败:', error)
    }
  }

  // 检查用户是否被限制预约 - 适配新的数据格式
  const isUserRestricted = computed(() => {
    const recentNoShows = noShowRecords.value.filter(record => {
      return dayjs().diff(dayjs(record.recordTime), 'day') <= 7
    })
    return recentNoShows.length >= 3
  })

  return {
    userInfo,
    busSchedules,
    myReservations,
    noShowRecords,
    noShowRecordsLoading,
    summerVacations,
    winterVacations,
    holidays,
    vehicles,
    drivers,
    scheduleAssignments,
    getScheduleByDate,
    canReserveDate,
    getAvailableDates,
    addReservation,
    cancelReservation,
    checkIn,
    recordNoShow,
    isUserRestricted,
    isVacationDate,
    addSummerVacation,
    updateSummerVacation,
    deleteSummerVacation,
    addWinterVacation,
    updateWinterVacation,
    deleteWinterVacation,
    addHoliday,
    removeHoliday,
    addVehicle,
    updateVehicle,
    deleteVehicle,
    addDriver,
    updateDriver,
    deleteDriver,
    saveScheduleAssignment,
    getScheduleAssignment,
    getAssignmentsByDate
  }
})
