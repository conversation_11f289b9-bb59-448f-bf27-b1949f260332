import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import { noshowRecordsApi } from '../api/noshowrecords'
// 导入auth工具
import { getToken, setToken } from '../utils/auth'

dayjs.extend(isBetween)

export const useBusStore = defineStore('bus', () => {
  // 用户信息 - 新的数据格式
  const userInfo = ref({
    roles: ['user'],
    user: {
      createBy: '',
      createTime: '',
      dept: {
        id: '',
        name: ''
      },
      enabled: true,
      id: '',
      jobs: [],
      nickName: '',
      phone: '',
      roles: [],
      updateBy: '',
      updateTime: '',
      username: ''
    }
  })

  const role = ref([])

  // 班车时刻表配置
  const busSchedules = ref({
    weekday: [
      {
        id: 'wd1',
        time: '06:30',
        route: '滨江至临安',
        stations: ['滨江校区', '汽车西站', '五常站', '青山湖地铁口']
      },
      {
        id: 'wd2',
        time: '11:50',
        route: '滨江至临安',
        stations: ['滨江校区', '汽车西站', '五常站', '青山湖地铁口']
      },
      {
        id: 'wd3',
        time: '16:20',
        route: '滨江至临安',
        stations: ['滨江校区', '汽车西站', '五常站', '青山湖地铁口']
      },
      {
        id: 'wd4',
        time: '12:10',
        route: '临安至滨江',
        stations: ['临安校区', '青山湖地铁口', '五常站', '汽车西站']
      },
      {
        id: 'wd5',
        time: '17:00',
        route: '临安至滨江',
        stations: ['临安校区', '青山湖地铁口', '五常站', '汽车西站']
      },
      {
        id: 'wd6',
        time: '21:20',
        route: '临安至滨江',
        stations: ['临安校区', '青山湖地铁口', '五常站', '汽车西站']
      }
    ],
    weekend: [
      {
        id: 'we1',
        time: '16:20',
        route: '滨江至临安',
        stations: ['滨江校区', '汽车西站', '五常站', '青山湖地铁口']
      },
      {
        id: 'we2',
        time: '18:30',
        route: '临安至滨江',
        stations: ['临安校区', '青山湖地铁口', '五常站', '汽车西站']
      }
    ],
    vacation: [
      {
        id: 'va1',
        time: '07:30',
        route: '滨江至临安',
        stations: ['滨江校区', '汽车西站', '五常站', '青山湖地铁口']
      },
      {
        id: 'va2',
        time: '16:30',
        route: '临安至滨江',
        stations: ['临安校区', '青山湖地铁口', '五常站', '汽车西站']
      }
    ]
  })

  // 我的预约记录
  const myReservations = ref([
  {
    id: 1,
    name: '张三',
    phone: '13800138001',
    dingUserId: 'user001',
    departureSpot: '滨江校区',
    busSchedules: {
      id: 1,
      start_station: '滨江校区',
      end_station: '临安校区',
      time: '06:30',
      date: '2025-09-11',
      station: '滨江校区',
      drivers: {
        id: 1,
        name: '张师傅',
        phone: '13800138001',
        dingUserId: 'driver001',
        licensePlate: '浙A12345'
      }
    },
    date: '2025-09-11',
    createTime:'2025-09-10 11:37:00',
    isRide: 'N',
    state: 'confirmed',
    signTime: null
  },
   {
    id: 2,
    name: '张三',
    phone: '13800138001',
    dingUserId: 'user001',
    departureSpot: '滨江校区',
    busSchedules: {
      id: 1,
      start_station: '滨江校区',
      end_station: '临安校区',
      time: '06:30',
      date: '2025-09-10',
      station: '滨江校区',
      drivers: {
        id: 1,
        name: '张师傅',
        phone: '13800138001',
        dingUserId: 'driver001',
        licensePlate: '浙A12345'
      }
    },
    date: '2025-09-10',
    createTime:'2025-09-08 10:04:00',
    isRide: 'N',
    state: 'completed',
    signTime:'2025-09-10 06:20'
  },
   {
    id: 2,
    name: '张三',
    phone: '13800138001',
    dingUserId: 'user001',
    departureSpot: '滨江校区',
    busSchedules: {
      id: 1,
      start_station: '滨江校区',
      end_station: '临安校区',
      time: '11:50',
      date: '2025-09-10',
      station: '滨江校区',
      drivers: {
        id: 1,
        name: '张师傅',
        phone: '13800138001',
        dingUserId: 'driver001',
        licensePlate: '浙A12345'
      }
    },
    date: '2025-09-09',
    createTime:'2025-09-07 12:11:00',
    isRide: 'N',
    state: 'completed',
    signTime:'2025-09-10 11:42'
  },
  {
    id: 3,
    name: '张三',
    phone: '13800138001',
    dingUserId: 'user001',
    departureSpot: '滨江校区',
    busSchedules: {
      id: 1,
      start_station: '滨江校区',
      end_station: '临安校区',
      time: '06:30',
      date: '2025-09-07',
      station: '滨江校区',
      drivers: {
        id: 1,
        name: '张师傅',
        phone: '13800138001',
        dingUserId: 'driver001',
        licensePlate: '浙A12345'
      }
    },
    date: '2025-09-06',
    createTime:'2025-09-06 11:27:00',
    isRide: 'N',
    state: 'cancelled',
    signTime: null
  },
  ])

  // 爽约记录
  const noShowRecords = ref({
    id: null,
    name: '',
    phone: '',
    num: 0,
    state: '',
    date: '',
    reservationsList: []
  })
  
  // 假期日期管理
  const summerVacations = ref([
    // 这里存储暑假日期区间
    // { year: 2023, start: '2023-07-01', end: '2023-08-31' },
    // { year: 2024, start: '2024-07-01', end: '2024-08-31' }
  ])

  const winterVacations = ref([
    // 这里存储寒假日期区间
    // { year: 2023, start: '2023-01-15', end: '2023-02-15' },
    // { year: 2024, start: '2024-01-15', end: '2024-02-15' }
  ])

  const holidays = ref([
    // 这里存储节假日日期
    // '2023-01-01', // 元旦
    // '2023-01-21', // 春节
    // '2023-01-22',
    // '2023-01-23',
  ])


  // 班次安排记录
  const scheduleAssignments = ref([
    // 这里存储班次安排信息
    // {
    //   id: '1',
    //   date: '2023-10-01',
    //   scheduleId: 'wd1',
    //   vehicleId: '1',
    //   driverId: '1',
    //   notes: '国庆期间正常运行'
    // }
  ])

  // 判断给定日期是否为假期
  const isVacationDate = (date) => {
    const targetDate = dayjs(date)
    const year = targetDate.year()
    const dateStr = targetDate.format('YYYY-MM-DD')

    // 检查是否为节假日
    if (holidays.value.includes(dateStr)) {
      return true
    }

    // 检查是否在暑假期间
    for (const vacation of summerVacations.value) {
      if (vacation.year === year && 
          targetDate.isBetween(vacation.start, vacation.end, 'day', '[]')) {
        return true
      }
    }

    // 检查是否在寒假期间
    for (const vacation of winterVacations.value) {
      if (vacation.year === year && 
          targetDate.isBetween(vacation.start, vacation.end, 'day', '[]')) {
        return true
      }
    }

    return false
  }

  // 获取指定日期的班车时刻表
  const getScheduleByDate = computed(() => {
    return (date) => {
      const dayOfWeek = dayjs(date).day()
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6
      const isVacation = isVacationDate(date) // 使用假期判断函数
      
      if (isVacation) {
        return busSchedules.value.vacation
      } else if (isWeekend) {
        // 周日才有班车，周六没有
        return dayOfWeek === 0 ? busSchedules.value.weekend : []
      } else {
        return busSchedules.value.weekday
      }
    }
  })

  // 假期管理相关方法
  const addSummerVacation = (vacation) => {
    const exists = summerVacations.value.some(v => 
      v.year === vacation.year && v.start === vacation.start && v.end === vacation.end
    )
    if (!exists) {
      summerVacations.value.push(vacation)
    }
  }

  const updateSummerVacation = (index, vacation) => {
    if (index >= 0 && index < summerVacations.value.length) {
      summerVacations.value[index] = vacation
    }
  }

  const deleteSummerVacation = (index) => {
    if (index >= 0 && index < summerVacations.value.length) {
      summerVacations.value.splice(index, 1)
    }
  }


  const updateWinterVacation = (index, vacation) => {
    if (index >= 0 && index < winterVacations.value.length) {
      winterVacations.value[index] = vacation
    }
  }

  const deleteWinterVacation = (index) => {
    if (index >= 0 && index < winterVacations.value.length) {
      winterVacations.value.splice(index, 1)
    }
  }

  const addHoliday = (date) => {
    if (!holidays.value.includes(date)) {
      holidays.value.push(date)
    }
  }

  // 班次安排相关方法
  const saveScheduleAssignment = (assignment) => {
    const existingIndex = scheduleAssignments.value.findIndex(a => 
      a.date === assignment.date && a.scheduleId === assignment.scheduleId
    )
    
    const newAssignment = {
      id: existingIndex >= 0 ? scheduleAssignments.value[existingIndex].id : Date.now().toString(),
      date: assignment.date,
      scheduleId: assignment.scheduleId,
      vehicleNumber: assignment.vehicleNumber,
      driverId: assignment.driverId,
      driverName: assignment.driverName
    }
    
    if (existingIndex >= 0) {
      scheduleAssignments.value[existingIndex] = newAssignment
    } else {
      scheduleAssignments.value.push(newAssignment)
    }
  }

  // 获取指定日期的所有班次安排
  const getAssignmentsByDate = computed(() => {
    return (date) => {
      return scheduleAssignments.value.filter(a => a.date === date)
    }
  })


  // 获取可预约的日期范围
  const getAvailableDates = computed(() => {
    const dates = []
    const now = dayjs()
    const cutoffTime = now.hour(18).minute(0).second(0)
    
    // 确定起始日期
    let startDate
    if (now.isAfter(cutoffTime)) {
      startDate = now.add(2, 'day') // 后天开始
    } else {
      startDate = now.add(1, 'day') // 明天开始
    }
    
    // 生成未来7天的可预约日期
    for (let i = 0; i < 7; i++) {
      const date = startDate.add(i, 'day')
      dates.push({
        date: date.format('YYYY-MM-DD'),
        dateText: date.format('MM月DD日'),
        dayText: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.day()],
        schedules: getScheduleByDate.value(date.format('YYYY-MM-DD'))
      })
    }
    
    return dates
  })

  // 添加预约
  const addReservation = (reservation) => {
    const newReservation = {
      id: Date.now().toString(),
      ...reservation,
      status: 'confirmed', // 直接确认，无需等待确认步骤
      createTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
    }
    myReservations.value.push(newReservation)
    return newReservation
  }

  // 取消预约
  const cancelReservation = (id) => {
    const index = myReservations.value.findIndex(r => r.id === id)
    if (index > -1) {
      myReservations.value[index].status = 'cancelled'
    }
  }

  // 签到确认
  const checkIn = (reservationId) => {
    const index = myReservations.value.findIndex(r => r.id === reservationId)
    if (index > -1) {
      myReservations.value[index].state = 'completed'
      myReservations.value[index].signTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    }
  }

  // 从接口获取我的爽约记录
  const recordNoShow = async () => {
    try {
      const params = {
        phone: userInfo.value.user?.phone || ''
      }
      const response = await noshowRecordsApi.myRecords(params)
      if(response) noShowRecords.value = response
        
    } catch (error) {
      console.error('获取爽约记录失败:', error)
    }
  }

 // 检查用户是否被限制预约 - 适配新的数据格式
  const isUserRestricted = computed(() => {
    if (!noShowRecords.value || !Array.isArray(noShowRecords.value)) {
      return false
    }
    
    const recentNoShows = noShowRecords.value.filter(record => {
      return dayjs().diff(dayjs(record.recordTime), 'day') <= 7
    })
    return recentNoShows.length >= 3
  })

  return {
    userInfo,
    role,
    busSchedules,
    myReservations,
    noShowRecords,
    summerVacations,
    winterVacations,
    holidays,
    scheduleAssignments,
    getScheduleByDate,
    getAvailableDates,
    addReservation,
    cancelReservation,
    checkIn,
    recordNoShow,
    isUserRestricted,
    isVacationDate,
    addSummerVacation,
    updateSummerVacation,
    deleteSummerVacation,
    updateWinterVacation,
    deleteWinterVacation,
    addHoliday,
    saveScheduleAssignment,
    getAssignmentsByDate,
  }
})
