import api from './index'

// 驾驶员管理接口
export const driversApi = {
  // 新增驾驶员
  create(data) {
    return api.post('/api/drivers', data)
  },

  // 修改驾驶员
  update(data) {
    return api.post('/api/drivers/put', data)
  },

  // 删除驾驶员
  delete(data) {
    return api.post('/api/drivers/del', data)
  },

  // 查询驾驶员列表
  list(params) {
    return api.get('/api/drivers', { params })
  },

  // 分页查询
  queryPage(params) {
    return api.get('/api/drivers/queryAllpage', { params })
  },

  // 查询空闲驾驶员
  getNotArranged(params) {
    return api.get('/api/drivers/notArranged', { params })
  }
}