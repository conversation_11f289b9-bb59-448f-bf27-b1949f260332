import api from './index'

// 班次管理接口
export const schedulesApi = {
  // 新增班次
  create(data) {
    return api.post('/api/schedules', data)
  },

  // 修改班次
  update(data) {
    return api.post('/api/schedules/put', data)
  },

  // 删除班次
  delete(data) {
    return api.post('/api/schedules/del', data)
  },

  // 查询班次列表
  list(params) {
    return api.get('/api/schedules', { params })
  },

  // 分页查询
  queryPage(params) {
    return api.get('/api/schedules/queryAllpage', { params })
  },

  // 安排驾驶员
  arrangeDrivers(data) {
    return api.post('/api/schedules/arrangeDrivers', data)
  }
}