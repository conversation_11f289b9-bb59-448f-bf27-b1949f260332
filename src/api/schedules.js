import api from './index'

// 班次管理接口
export const schedulesApi = {
  // 新增班次
  create(data) {
    return api.post('/api/schedules', data)
  },

  // 修改班次
  update(data) {
    return api.post('/api/schedules/put', data)
  },

  // 删除班次
  delete(data) {
    return api.post('/api/schedules/del', data)
  },

  // 查询班次列表
  list(params) {
    return api.get('/api/schedules', { params })
  },

  // 分页查询
  queryPage(params) {
    return api.get('/api/schedules/queryAllpage', { params })
  },

  // 安排驾驶员及车牌
  //   {
  //   "id": Long, // 班次ID
  //   "start_station": String, // 出发地
  //   "end_station": String, // 目的地
  //   "busTime": String, // 时间
  //   "busDate": String, // 日期
  //   "station": String, // 站点
  //   "drivers": {
  //     "id": Long, // 驾驶员ID
  //     "name": String, // 驾驶员姓名
  //     "phone": String, // 驾驶员手机号
  //   },
  //   "licensePlate": String // 车牌
  // }
  arrangeDrivers(data) {
    return api.post('/api/schedules/arrangeDrivers', data)
  }
}