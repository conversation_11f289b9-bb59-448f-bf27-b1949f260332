import api from './index'

// 爽约管理接口
export const noshowRecordsApi = {
  // 新增爽约记录
  create(data) {
    return api.post('/api/noshowrecords', data)
  },

  // 修改爽约记录
  update(data) {
    return api.post('/api/noshowrecords/put', data)
  },

  // 删除爽约记录
  delete(data) {
    return api.post('/api/noshowrecords/del', data)
  },

  // 查询爽约记录列表
  list(params) {
    return api.get('/api/noshowrecords', { params })
  },

  // 分页查询
  queryPage(params) {
    return api.get('/api/noshowrecords/queryAllpage', { params })
  },

  // 我的爽约记录
  myRecords(params) {
    return api.get('/api/noshowrecords/myRecords', { params })
  },

  // 统计
  statistics(params) {
    return api.get('/api/noshowrecords/statistics', { params })
  }
}