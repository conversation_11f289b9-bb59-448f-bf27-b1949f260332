import api from './index'

// 预约管理接口
export const reservationApi = {
  // 新增预约
  create(data) {
    return api.post('/api/reservation', data)
  },

  // 修改预约
  update(data) {
    return api.post('/api/reservation/put', data)
  },

  // 删除预约
  delete(data) {
    return api.post('/api/reservation/del', data)
  },

  // 查询预约列表
  list(params) {
    return api.get('/api/reservation', { params })
  },

  // 分页查询
  queryPage(params) {
    return api.get('/api/reservation/queryAllpage', { params })
  },

  // 签到
  sign(data) {
    return api.post('/api/reservation/sign', data)
  },

  // 统计
  statistics(params) {
    return api.get('/api/reservation/statistics', { params })
  },

  // 推送车辆信息
  informationPush(params) {
    return api.get('/api/reservation/informationPush', { params })
  }
}