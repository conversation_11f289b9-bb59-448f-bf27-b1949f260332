import api from './index'

// 预约管理接口
export const reservationApi = {
  // 新增预约
  create(data) {
    return api.post('/api/reservation', data)
  },

  // 修改预约
  update(data) {
    return api.post('/api/reservation/put', data)
  },

  // 删除预约
  delete(data) {
    return api.post('/api/reservation/del', data)
  },

  // 查询预约列表
  list(params) {
    return api.get('/api/reservation', { params })
  },

  // 分页查询
  //   - **接口地址**: `GET /api/reservation/queryAllpage`
  // - **接口描述**: 分页查询预约
  // - **请求参数**:
  //   - phone: String // 手机号
  //   - isRide: String // 签到（0:否，1:是）
  //   - state: String // 状态(0:已预约，1：已完成，2：已取消)
  //   - date: String // 日期
  //   - schedulesId: Long // 班次ID
  //   - page: Integer // 页码
  //   - size: Integer // 每页数量
  // - **响应**:
  // ```json
  // {
  //   "content": [
  //     {
  //       "id": Long, // 预约ID
  //       "name": String, // 姓名
  //       "phone": String, // 手机号
  //       "dingUserId": String, // 钉钉用户ID
  //       "departureSpot": String, // 上车地点
  //       "busSchedules": {
  //         "id": Long, // 班次ID
  //         "start_station": String, // 出发地
  //         "end_station": String, // 目的地
  //         "busTime": String, // 时间
  //         "busDate": String, // 日期
  //         "station": String, // 站点
  //         "drivers": {
  //           "id": Long, // 驾驶员ID
  //           "name": String, // 驾驶员姓名
  //           "phone": String, // 驾驶员手机号
  //           "dingUserId": String, // 驾驶员钉钉ID
  //           "licensePlate": String // 车牌
  //         }
  //       },
  //       "reservationDate": String, // 预约日期
  //       "isRide": String, // 签到（0:否，1:是）
  //       "state": String, // 状态（0:已预约，1:已完成，2:已取消）
  //       "signTime": String // 签到时间
  //     }
  //   ],
  //   "totalElements": Integer, // 总条数
  //   "totalPages": Integer, // 总页数
  //   "page": Integer, // 当前页
  //   "size": Integer // 每页数量
  // }
  // ```

  queryPage(params) {
    return api.get('/api/reservation/queryAllpage', { params })
  },

  // 签到
  sign(data) {
    return api.post('/api/reservation/sign', data)
  },

  // 统计
  statistics(params) {
    return api.get('/api/reservation/statistics', { params })
  },

  // 推送车辆信息
  informationPush(params) {
    return api.get('/api/reservation/informationPush', { params })
  }
}