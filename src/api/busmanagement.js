import api from './index'

// 车辆管理接口
// - **接口描述**: 车辆信息查询
// - **响应**:
// ```json
// [
//   {
//     "id": Long, // 记录ID
//     "name": String, // 姓名
//     "phone": String, // 手机号
//     "num": Integer, // 爽约次数
//     "state": String, // 状态
//     "limitedDate": String // 限制日期
//   }
// ]
export const busmanagementApi = {
  // 查询驾驶员列表
  list(params) {
    return api.get('/api/busmanagement', { params })
  },
}