import axios from 'axios'
import { showToast } from 'vant'
import { getToken } from "@/utils/auth";


// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 10000,
});


// 请求拦截器
api.interceptors.request.use(
  config => {
   if (getToken()) {
      config.headers["Authorization"] = getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    config.headers["Content-Type"] = "application/json";
    console.log('API请求:', config.method?.toUpperCase(), config.url, config.data || config.params)
    return config
  },
  error => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('API响应:', response.config.url, response.data)

    // 根据接口文档，成功的响应直接返回数据
    if (response.status >= 200 && response.status < 300) {
      return response.data
    }

    return response.data
  },
  error => {
    console.error('API错误:', error.response || error)

    let message = '网络错误'
    if (error.response) {
      const { status, data } = error.response
      switch (status) {
        case 400:
          message = data?.message || '请求参数错误'
          break
        case 401:
          message = '未授权访问'
          break
        case 403:
          message = '权限不足'
          break
        case 404:
          message = '资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = data?.message || `请求失败 (${status})`
      }
    } else if (error.code === 'ECONNABORTED') {
      message = '请求超时'
    }

    showToast(message)
    return Promise.reject(error)
  }
)

export default api