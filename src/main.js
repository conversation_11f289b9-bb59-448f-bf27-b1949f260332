import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import Vant from 'vant'
import 'vant/lib/index.css'
import './assets/main.css'

// 在开发环境下引入vconsole
if (import.meta.env.VITE_APP_ENV==="development") {
  import('vconsole').then(VConsole => {
    new VConsole.default()
  })
}

import App from './App.vue'
import routes, { setupRouterGuard } from './router/index.js'

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 设置路由守卫
setupRouterGuard(router)

const pinia = createPinia()
const app = createApp(App)

app.use(pinia)
app.use(router)
app.use(Vant)

app.mount('#app')