<template>
  <div class="my-reservations-page">
    <div class="page-container">
      <!-- 状态筛选 -->
      <div class="filter-tabs">
        <van-tabs v-model:active="activeTab" @change="onTabChange">
          <van-tab title="全部" name="all"></van-tab>
          <van-tab title="已预约" name="0"></van-tab>
          <van-tab title="已完成" name="1"></van-tab>
          <van-tab title="已取消" name="2"></van-tab>
        </van-tabs>
      </div>

      <!-- 预约列表 -->
      <div class="reservation-list">
        <div 
          v-for="reservation in filteredReservations" 
          :key="reservation.id"
          class="reservation-item card"
        >
          <!-- 预约状态标签 -->
          <div class="reservation-header">
            <van-tag 
              :type="getStatusTagType(reservation.state || reservation.status)"
              size="medium"
            >
              {{ getStatusText(reservation.state || reservation.status) }}
            </van-tag>
            <div class="reservation-time">
              {{ formatCreateTime(reservation.createTime) }}
            </div>
          </div>

          <!-- 班车信息 -->
          <div class="bus-info">
            <div class="bus-main">
              <div class="bus-time">{{ getBusTime(reservation) }}</div>
              <div class="bus-route">{{ getBusRoute(reservation) }}</div>
            </div>
            <div class="bus-date">
              <van-icon name="calendar-o" />
              {{ formatDate(reservation.date) }}
            </div>
          </div>

          <!-- 乘车信息 -->
          <div class="ride-info">
            <div class="info-item">
              <van-icon name="location-o" />
              <span>上车站点：{{ getStation(reservation) }}</span>
            </div>
            <div class="info-item" v-if="reservation.phone">
              <van-icon name="phone-o" />
              <span>联系电话：{{ reservation.phone }}</span>
            </div>
          </div>

          <!-- 签到信息 -->
          <div class="checkin-info" v-if="getCheckInTime(reservation)">
            <van-icon name="checked" color="#07c160" />
            <span>签到时间：{{ formatDateTime(getCheckInTime(reservation)) }}</span>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons" v-if="canOperate(reservation)">
            <van-button
              size="small"
              type="danger"
              plain
              @click="cancelReservation(reservation)"
              v-if="reservation.state === '0' || reservation.status === '0'"
            >
              取消预约
            </van-button>
            <van-button 
              size="small" 
              type="primary"
              @click="goToCheckIn(reservation)"
              v-if="(reservation.state === '0' || reservation.status === '0') && isToday(reservation.date)"
            >
              立即签到
            </van-button>
          </div>
        </div>

        <!-- 空状态 -->
        <van-empty 
          v-if="filteredReservations.length === 0"
          :description="getEmptyDescription()"
          image="https://fastly.jsdelivr.net/npm/@vant/assets/custom-empty-image.png"
        >
          <van-button
            type="primary"
            size="small"
            @click="goToReservation"
          >
            立即预约
          </van-button>
        </van-empty>
      </div>
    </div>

    <!-- 取消预约确认弹窗 -->
    <van-dialog
      v-model:show="showCancelDialog"
      title="取消预约"
      message="确定要取消这个预约吗？"
      show-cancel-button
      @confirm="confirmCancel"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useBusStore } from '../stores/bus.js'
import { reservationApi } from '../api/reservation.js'
import { showToast } from 'vant'
import dayjs from 'dayjs'

const router = useRouter()
const busStore = useBusStore()

// 响应式数据
const activeTab = ref('all')
const showCancelDialog = ref(false)
const selectedReservation = ref(null)
const myReservations = ref([])
const loading = ref(false)
const error = ref('')

// 计算属性
const filteredReservations = computed(() => {
  if (activeTab.value === 'all') {
    return myReservations.value
  }
  return myReservations.value.filter(r => {
    // 适配 state 字段而不是 status 字段
    const reservationState = r.state || r.status
    return reservationState === activeTab.value
  })
})

// 方法
const getStatusText = (state) => {
  // 适配接口返回的状态码 0:已预约，1:已完成，2:已取消
  const statusMap = {
    '0': '已预约',
    '1': '已完成',
    '2': '已取消'
  }
  return statusMap[state] || '未知'
}

const getStatusTagType = (state) => {
  // 适配接口返回的状态码 0:已预约，1:已完成，2:已取消
  const typeMap = {
    '0': 'primary',
    '1': 'success',
    '2': 'default'
  }
  return typeMap[state] || 'default'
}

const formatDate = (date) => {
  return date ? dayjs(date).format('MM月DD日') : ''
}

const formatDateTime = (datetime) => {
  return dayjs(datetime).format('MM月DD日 HH:mm')
}

const formatCreateTime = (time) => {
  // 如果没有 createTime 字段，可以使用当前日期时间作为默认值
  return time ? dayjs(time).format('MM-DD HH:mm') : dayjs().format('MM-DD HH:mm')
}

const isToday = (date) => {
  return dayjs(date).isSame(dayjs(), 'day')
}

const canOperate = (reservation) => {
  // 只有已预约的记录可以操作
  const state = reservation.state || reservation.status
  return state === '0'
}

const getEmptyDescription = () => {
  const descriptions = {
    all: '暂无预约记录',
    '0': '暂无已预约的班车',
    '1': '暂无已完成的预约',
    '2': '暂无已取消的预约'
  }
  return descriptions[activeTab.value] || '暂无数据'
}

const onTabChange = (name) => {
  activeTab.value = name
}

const cancelReservation = (reservation) => {
  selectedReservation.value = reservation
  showCancelDialog.value = true
}

const confirmCancel = async () => {
  if (selectedReservation.value) {
    try {
      loading.value = true
      await reservationApi.delete({ id: selectedReservation.value.id })
      showToast('预约已取消')
      // 重新加载预约列表
      await fetchMyReservations()
    } catch (err) {
      console.error('取消预约失败:', err)
      showToast('取消预约失败，请稍后重试')
    } finally {
      loading.value = false
    }
    selectedReservation.value = null
  }
  showCancelDialog.value = false
}

const goToCheckIn = (reservation) => {
  router.push({
    name: 'CheckIn',
    query: {
      reservationId: reservation.id
    }
  })
}

const goToReservation = () => {
  router.push({
    name: 'Home'
  })
}

const getBusTime = (reservation) => {
  // 从 busSchedules.busTime 获取时间
  return reservation.busSchedules ? (reservation.busSchedules.busTime || reservation.busSchedules.time) : reservation.busTime || ''
}

const getBusRoute = (reservation) => {
  // 从 busSchedules.start_station 和 end_station 构建路线
  if (reservation.busSchedules) {
    return `${reservation.busSchedules.start_station}至${reservation.busSchedules.end_station}`
  }
  return reservation.busRoute || ''
}

const getStation = (reservation) => {
  // 获取上车站点
  if (reservation.busSchedules) {
    return reservation.busSchedules.station || reservation.departureSpot || ''
  }
  return reservation.station || reservation.departureSpot || ''
}

const getCheckInTime = (reservation) => {
  // 适配 signTime 字段而不是 checkInTime
  return reservation.signTime || reservation.checkInTime
}

// 获取我的预约列表
const fetchMyReservations = async () => {
  try {
    loading.value = true
    error.value = ''
    // 使用分页查询接口获取数据
    const params = {
      phone: busStore.userInfo.user?.phone || '',
      page: 0,
      size: 100 // 获取足够多的记录
    }
    const response = await reservationApi.queryPage(params)
    
    if (response && response.content) {
      // 处理接口返回的数据，确保状态字段与前端匹配
      myReservations.value = response.content.map(item => ({
        ...item,
        // 确保状态字段为字符串类型
        state: String(item.state || 'unknown'),
        busSchedules: item.busSchedules || {}
      }))
    }
  } catch (err) {
    console.error('获取预约列表失败:', err)
    error.value = '获取预约列表失败，请稍后重试'
    showToast('获取预约列表失败，请稍后重试')
    // 在请求失败时使用模拟数据作为备用
    myReservations.value = busStore.myReservations
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchMyReservations()
})
</script>

<style scoped>
.my-reservations-page {
  width: 100%;
  min-height: 100%;
  background: #f7f8fa;
  overflow-x: hidden;
}

.filter-tabs {
  background: white;
  margin-bottom: 8px;
}

.reservation-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reservation-item {
  position: relative;
}

.reservation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.reservation-time {
  font-size: 12px;
  color: #969799;
}

.bus-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebedf0;
}

.bus-main {
  flex: 1;
}

.bus-time {
  font-size: 20px;
  font-weight: 600;
  color: #1989fa;
  margin-bottom: 4px;
}

.bus-route {
  font-size: 14px;
  color: #646566;
}

.bus-date {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #646566;
}

.ride-info {
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #646566;
}

.info-item:last-child {
  margin-bottom: 0;
}

.checkin-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #07c160;
  margin-bottom: 12px;
  padding: 8px;
  background: #f0f9ff;
  border-radius: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}
</style>
