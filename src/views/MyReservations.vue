<template>
  <div class="my-reservations-page">
    <div class="page-container">
      <!-- 状态筛选 -->
      <div class="filter-tabs">
        <van-tabs v-model:active="activeTab" @change="onTabChange">
          <van-tab title="全部" name="all"></van-tab>
          <van-tab title="已预约" name="0"></van-tab>
          <van-tab title="已完成" name="1"></van-tab>
          <van-tab title="已取消" name="2"></van-tab>
        </van-tabs>
      </div>

      <!-- 预约列表 -->
      <div class="reservation-list">
        <!-- 初始加载骨架屏 -->
        <div v-if="loading && displayReservations.length === 0" class="skeleton-container">
          <van-skeleton
            v-for="i in 3"
            :key="i"
            :row="4"
            :loading="true"
            class="skeleton-item"
          />
        </div>

        <!-- 正常列表内容 -->
        <van-pull-refresh
          v-else
          v-model="refreshing"
          @refresh="onRefresh"
          success-text="刷新成功"
        >
          <van-list
            v-model:loading="listLoading"
            :finished="listFinished"
            finished-text="没有更多了"
            @load="onLoadMore"
            :immediate-check="false"
          >
          <div
            v-for="reservation in displayReservations"
            :key="reservation.id"
            class="reservation-item card"
          >
            <!-- 预约状态标签 -->
            <div class="reservation-header">
              <van-tag
                :type="getStatusTagType(reservation.state)"
                size="medium"
              >
                {{ getStatusText(reservation.state) }}
              </van-tag>
              <div class="reservation-time">
                {{ formatCreateTime(reservation.createTime) }}
              </div>
            </div>

            <!-- 班车信息 -->
            <div class="bus-info">
              <div class="bus-main">
                <div class="bus-time">{{ getBusTime(reservation) }}</div>
                <div class="bus-route">{{ getBusRoute(reservation) }}</div>
              </div>
              <div class="bus-date">
                <van-icon name="calendar-o" />
                {{ getBusDate(reservation) }}
              </div>
            </div>

            <!-- 乘车信息 -->
            <div class="ride-info">
              <div class="info-item">
                <van-icon name="location-o" />
                <span>上车站点：{{ reservation.departureSpot }}</span>
              </div>
              <div class="info-item" v-if="reservation.phone">
                <van-icon name="phone-o" />
                <span>联系电话：{{ reservation.phone }}</span>
              </div>
            </div>

            <!-- 签到信息 -->
            <div class="checkin-info" v-if="reservation.signTime">
              <van-icon name="checked" color="#07c160" />
              <span>签到时间：{{ reservation.signTime }}</span>
            </div>
              <!-- <van-button
                size="small"
                type="danger"
                plain
                @click="test(reservation)"
              >
                测试
              </van-button> -->
            <!-- 操作按钮 -->
            <div class="action-buttons" v-if="reservation.state==='0'">
              <van-button
                size="small"
                type="danger"
                plain
                @click="cancelReservation(reservation)"
                v-if="reservation.state === '0'"
              >
                取消预约
              </van-button>
             
              <van-button
                size="small"
                type="primary"
                @click="goToCheckIn(reservation)"
                v-if="(reservation.state === '0') && isToday(reservation.date)"
              >
                立即签到
              </van-button>
            </div>
          </div>

          <!-- 空状态 -->
          <template #finished>
            <van-empty
              v-if="displayReservations.length === 0 && !listLoading"
              :description="getEmptyDescription()"
              image="https://fastly.jsdelivr.net/npm/@vant/assets/custom-empty-image.png"
            >
              <van-button
                type="primary"
                size="small"
                @click="goToReservation"
              >
                立即预约
              </van-button>
            </van-empty>
          </template>
          </van-list>
        </van-pull-refresh>
      </div>
    </div>

    <!-- 取消预约确认弹窗 -->
    <van-dialog
      v-model:show="showCancelDialog"
      title="取消预约"
      message="确定要取消这个预约吗？"
      show-cancel-button
      @confirm="confirmCancel"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useBusStore } from '../stores/bus.js'
import { reservationApi } from '../api/reservation.js'
import { showToast } from 'vant'
import dayjs from 'dayjs'

const router = useRouter()
const busStore = useBusStore()

// 响应式数据
const activeTab = ref('all')
const showCancelDialog = ref(false)
const selectedReservation = ref(null)
const displayReservations = ref([]) // 当前显示的预约数据
const loading = ref(false)
const error = ref('')

// 分页相关状态
const listLoading = ref(false)
const listFinished = ref(false)
const refreshing = ref(false)
const currentPage = ref(0)
const pageSize = ref(10)
const totalElements = ref(0)

// 计算属性 - 现在数据直接从API按状态获取，不需要前端筛选

// 方法
const getStatusText = (state) => {
  // 适配接口返回的状态码 0:已预约，1:已完成，2:已取消
  const statusMap = {
    '0': '已预约',
    '1': '已完成',
    '2': '已取消'
  }
  return statusMap[state] || '未知'
}

const getStatusTagType = (state) => {
  // 适配接口返回的状态码 0:已预约，1:已完成，2:已取消
  const typeMap = {
    '0': 'primary',
    '1': 'success',
    '2': 'default'
  }
  return typeMap[state] || 'default'
}

const formatCreateTime = (time) => {
  // 如果没有 createTime 字段，可以使用当前日期时间作为默认值
  return time ? dayjs(time).format('MM-DD HH:mm') : dayjs().format('MM-DD HH:mm')
}

const isToday = (date) => {
  return dayjs(date).isSame(dayjs(), 'day')
}

const getEmptyDescription = () => {
  const descriptions = {
    all: '暂无预约记录',
    '0': '暂无已预约的班车',
    '1': '暂无已完成的预约',
    '2': '暂无已取消的预约'
  }
  return descriptions[activeTab.value] || '暂无数据'
}

const onTabChange = async (name) => {
  activeTab.value = name
  // 切换标签时重置分页状态并重新请求数据
  resetPagination()

  // 根据选中的状态重新请求数据
  await fetchMyReservations(name, 0, pageSize.value)
}

// 重置分页状态
const resetPagination = () => {
  displayReservations.value = []
  currentPage.value = 0
  listFinished.value = false
  listLoading.value = false
  totalElements.value = 0
}

// List组件的加载更多回调
const onLoadMore = async () => {
  if (listFinished.value || listLoading.value) return

  listLoading.value = true

  try {
    // 请求下一页数据
    await fetchMyReservations(activeTab.value, currentPage.value, pageSize.value)
  } catch (error) {
    console.error('加载更多数据失败:', error)
    showToast('加载失败，请重试')
  } finally {
    listLoading.value = false
  }
}

// 下拉刷新回调
const onRefresh = async () => {
  try {
    refreshing.value = true
    // 重置分页状态
    resetPagination()
    // 根据当前选中的状态重新请求第一页数据
    await fetchMyReservations(activeTab.value, 0, pageSize.value)
    showToast('刷新成功')
  } catch (error) {
    showToast('刷新失败')
  } finally {
    refreshing.value = false
  }
}

const cancelReservation = (reservation) => {
  selectedReservation.value = reservation
  showCancelDialog.value = true
}

const test = async (reservation) => {
  var params = {
        ...reservation,
        state: '0'
      }
      await reservationApi.update(params)
}

const confirmCancel = async () => {
  if (selectedReservation.value) {
    try {
      loading.value = true
      var params = {
        ...selectedReservation.value,
        state: '2'
      }
      await reservationApi.update(params)
      showToast('预约已取消')

      // 重新加载显示数据
      resetPagination()
      await fetchMyReservations(activeTab.value, 0, pageSize.value)
    } catch (err) {
      console.error('取消预约失败:', err)
      showToast('取消预约失败，请稍后重试')
    } finally {
      loading.value = false
    }
    selectedReservation.value = null
  }
  showCancelDialog.value = false
}

const goToCheckIn = (reservation) => {
  router.push({
    name: 'CheckIn',
    query: {
      reservationId: reservation.id
    }
  })
}

const goToReservation = () => {
  router.push({
    name: 'Home'
  })
}

const getBusTime = (reservation) => {
  // 从 busSchedules.busTime 获取时间
  return reservation.busSchedules ? (reservation.busSchedules.busTime || reservation.busSchedules.time) : reservation.busTime || ''
}

const getBusDate = (reservation) => {
  // 从 busSchedules.busDate 获取时间
  return reservation.busSchedules ? (reservation.busSchedules.busDate || reservation.busSchedules.date) : reservation.busDate || ''
}

const getBusRoute = (reservation) => {
  // 从 busSchedules.start_station 和 end_station 构建路线
  if (reservation.busSchedules) {
    return `${reservation.busSchedules.start_station} → ${reservation.busSchedules.end_station}`
  }
  return reservation.busRoute || ''
}

// 获取我的预约列表
const fetchMyReservations = async (status = null, page = 0, size = 10) => {
  try {
    loading.value = true
    error.value = ''

    // 构建请求参数
    const params = {
      phone: busStore.userInfo.user?.phone || '',
      page: page,
      size: size
    }

    // 如果指定了状态，添加状态筛选参数
    if (status && status !== 'all') {
      params.state = status
    }

    console.log('请求参数:', params)
    const response = await reservationApi.queryPage(params)

    if (response && response.content) {
      // 处理接口返回的数据，确保状态字段与前端匹配
      const processedData = response.content.map(item => ({
        ...item,
        // 确保状态字段为字符串类型
        state: String(item.state || 'unknown'),
        busSchedules: item.busSchedules || {}
      }))

      if (page === 0) {
        // 第一页，直接设置数据
        displayReservations.value = processedData
      } else {
        // 后续页，追加数据
        displayReservations.value.push(...processedData)
      }

      // 设置总数据量和分页状态
      totalElements.value = response.totalElements || response.content.length
      currentPage.value = page + 1

      // 检查是否还有更多数据
      if (processedData.length < size || displayReservations.value.length >= totalElements.value) {
        listFinished.value = true
      } else {
        listFinished.value = false
      }

    } else {
      // 没有数据时的处理
      if (page === 0) {
        displayReservations.value = []
      }
      listFinished.value = true
    }
  } catch (err) {
    console.error('获取预约列表失败:', err)
    error.value = '获取预约列表失败，请稍后重试'
    showToast('获取预约列表失败，请稍后重试')

    // 在请求失败时的处理
    if (page === 0) {
      displayReservations.value = []
    }
    listFinished.value = true
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  // 初始加载全部数据
  fetchMyReservations('all', 0, pageSize.value)
})
</script>

<style scoped>
.my-reservations-page {
  width: 100%;
  min-height: 100%;
  background: #f7f8fa;
  overflow-x: hidden;
}

.filter-tabs {
  background: white;
  margin-bottom: 8px;
}

.reservation-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reservation-item {
  position: relative;
}

.reservation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.reservation-time {
  font-size: 12px;
  color: #969799;
}

.bus-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebedf0;
}

.bus-main {
  flex: 1;
}

.bus-time {
  font-size: 20px;
  font-weight: 600;
  color: #1989fa;
  margin-bottom: 4px;
}

.bus-route {
  font-size: 14px;
  color: #646566;
}

.bus-date {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #646566;
}

.ride-info {
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #646566;
}

.info-item:last-child {
  margin-bottom: 0;
}

.checkin-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #07c160;
  margin-bottom: 12px;
  padding: 8px;
  background: #f0f9ff;
  border-radius: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* 骨架屏样式 */
.skeleton-container {
  padding: 0 16px;
}

.skeleton-item {
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
