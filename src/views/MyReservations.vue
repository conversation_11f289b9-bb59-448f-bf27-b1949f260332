<template>
  <div class="my-reservations-page">
    <div class="page-container">
      <!-- 状态筛选 -->
      <div class="filter-tabs">
        <van-tabs v-model:active="activeTab" @change="onTabChange">
          <van-tab title="全部" name="all"></van-tab>
          <van-tab title="已预约" name="confirmed"></van-tab>
          <van-tab title="已完成" name="completed"></van-tab>
          <van-tab title="已取消" name="cancelled"></van-tab>
        </van-tabs>
      </div>

      <!-- 预约列表 -->
      <div class="reservation-list">
        <div 
          v-for="reservation in filteredReservations" 
          :key="reservation.id"
          class="reservation-item card"
        >
          <!-- 预约状态标签 -->
          <div class="reservation-header">
            <van-tag 
              :type="getStatusTagType(reservation.status)"
              size="medium"
            >
              {{ getStatusText(reservation.status) }}
            </van-tag>
            <div class="reservation-time">
              {{ formatCreateTime(reservation.createTime) }}
            </div>
          </div>

          <!-- 班车信息 -->
          <div class="bus-info">
            <div class="bus-main">
              <div class="bus-time">{{ reservation.busTime }}</div>
              <div class="bus-route">{{ reservation.busRoute }}</div>
            </div>
            <div class="bus-date">
              <van-icon name="calendar-o" />
              {{ formatDate(reservation.date) }}
            </div>
          </div>

          <!-- 乘车信息 -->
          <div class="ride-info">
            <div class="info-item">
              <van-icon name="location-o" />
              <span>上车站点：{{ reservation.station }}</span>
            </div>
            <div class="info-item" v-if="reservation.phone">
              <van-icon name="phone-o" />
              <span>联系电话：{{ reservation.phone }}</span>
            </div>

          </div>

          <!-- 签到信息 -->
          <div class="checkin-info" v-if="reservation.checkInTime">
            <van-icon name="checked" color="#07c160" />
            <span>签到时间：{{ formatDateTime(reservation.checkInTime) }}</span>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons" v-if="canOperate(reservation)">
            <van-button
              size="small"
              type="danger"
              plain
              @click="cancelReservation(reservation)"
              v-if="reservation.status === 'confirmed'"
            >
              取消预约
            </van-button>
            <van-button 
              size="small" 
              type="primary"
              @click="goToCheckIn(reservation)"
              v-if="reservation.status === 'confirmed' && isToday(reservation.date)"
            >
              立即签到
            </van-button>
          </div>
        </div>

        <!-- 空状态 -->
        <van-empty 
          v-if="filteredReservations.length === 0"
          :description="getEmptyDescription()"
          image="https://fastly.jsdelivr.net/npm/@vant/assets/custom-empty-image.png"
        >
          <van-button
            type="primary"
            size="small"
            @click="goToReservation"
          >
            立即预约
          </van-button>
        </van-empty>
      </div>


    </div>

    <!-- 取消预约确认弹窗 -->
    <van-dialog
      v-model:show="showCancelDialog"
      title="取消预约"
      message="确定要取消这个预约吗？"
      show-cancel-button
      @confirm="confirmCancel"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useBusStore } from '../stores/bus.js'
import { showToast } from 'vant'
import dayjs from 'dayjs'

const router = useRouter()
const busStore = useBusStore()

// 响应式数据
const activeTab = ref('all')
const showCancelDialog = ref(false)
const selectedReservation = ref(null)

// 计算属性
const myReservations = computed(() => busStore.myReservations)
const filteredReservations = computed(() => {
  if (activeTab.value === 'all') {
    return myReservations.value
  }
  return myReservations.value.filter(r => r.status === activeTab.value)
})

// 方法
const getStatusText = (status) => {
  const statusMap = {
    confirmed: '已预约',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知'
}

const getStatusTagType = (status) => {
  const typeMap = {
    confirmed: 'primary',
    completed: 'success',
    cancelled: 'default'
  }
  return typeMap[status] || 'default'
}

const formatDate = (date) => {
  return dayjs(date).format('MM月DD日')
}

const formatDateTime = (datetime) => {
  return dayjs(datetime).format('MM月DD日 HH:mm')
}

const formatCreateTime = (time) => {
  return dayjs(time).format('MM-DD HH:mm')
}

const isToday = (date) => {
  return dayjs(date).isSame(dayjs(), 'day')
}

const canOperate = (reservation) => {
  // 只有已预约的记录可以操作
  return reservation.status === 'confirmed'
}

const getEmptyDescription = () => {
  const descriptions = {
    all: '暂无预约记录',
    confirmed: '暂无已预约的班车',
    completed: '暂无已完成的预约',
    cancelled: '暂无已取消的预约'
  }
  return descriptions[activeTab.value] || '暂无数据'
}

const onTabChange = (name) => {
  activeTab.value = name
}

const cancelReservation = (reservation) => {
  selectedReservation.value = reservation
  showCancelDialog.value = true
}

const confirmCancel = () => {
  if (selectedReservation.value) {
    busStore.cancelReservation(selectedReservation.value.id)
    showToast('预约已取消')
    selectedReservation.value = null
  }
  showCancelDialog.value = false
}

const goToCheckIn = (reservation) => {
  router.push({
    name: 'CheckIn',
    query: {
      reservationId: reservation.id
    }
  })
}

const goToReservation = () => {
  router.push({
    name: 'Home'
  })
}

// 生命周期
onMounted(() => {
  // 可以在这里加载预约数据
})
</script>

<style scoped>
.my-reservations-page {
  width: 100%;
  min-height: 100%;
  background: #f7f8fa;
  overflow-x: hidden;
}

.filter-tabs {
  background: white;
  margin-bottom: 8px;
}

.reservation-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reservation-item {
  position: relative;
}

.reservation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.reservation-time {
  font-size: 12px;
  color: #969799;
}

.bus-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebedf0;
}

.bus-main {
  flex: 1;
}

.bus-time {
  font-size: 20px;
  font-weight: 600;
  color: #1989fa;
  margin-bottom: 4px;
}

.bus-route {
  font-size: 14px;
  color: #646566;
}

.bus-date {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #646566;
}

.ride-info {
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #646566;
}

.info-item:last-child {
  margin-bottom: 0;
}

.checkin-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #07c160;
  margin-bottom: 12px;
  padding: 8px;
  background: #f0f9ff;
  border-radius: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}
</style>
