<template>
  <div class="vacation-management-page">
    <div class="page-container">
      <!-- 假期类型选择 -->
      <van-tabs v-model:active="activeTab" @change="onTabChange" class="tab-container">
        <van-tab title="寒暑假管理" name="vacation"></van-tab>
        <van-tab title="节假日管理" name="holiday"></van-tab>
      </van-tabs>
      
      <!-- 寒暑假管理 -->
      <div v-if="activeTab === 'vacation'" class="vacation-content">
        <div class="button-group">
          <van-button type="primary" @click="showAddVacationPanel">
            <van-icon name="plus" /> 添加寒暑假
          </van-button>
        </div>
        
        <div class="vacation-list" v-if="vacations.length > 0">
          <div 
            v-for="vacation in vacations" 
            :key="vacation.id"
            class="vacation-item card"
          >
            <div class="vacation-info">
              <div class="vacation-name">{{ vacation.name }}</div>
              <div class="vacation-period">
                {{ formatDate(vacation.startDate) }} 至 {{ formatDate(vacation.endDate) }}
              </div>
              <div class="vacation-desc">{{ vacation.description }}</div>
            </div>
            <div class="vacation-actions">
              <van-button size="small" type="primary" @click="editVacation(vacation)">
                编辑
              </van-button>
              <van-button size="small" type="danger" @click="deleteVacation(vacation.id, vacation.type)">
                删除
              </van-button>
            </div>
          </div>
        </div>
        
        <van-empty v-else description="暂无寒暑假记录" />
      </div>
      
      <!-- 节假日管理 -->
      <div v-if="activeTab === 'holiday'" class="holiday-content">
        <div class="button-group">
          <van-button type="primary" @click="showAddHolidayPanel">
            <van-icon name="plus" /> 添加节假日
          </van-button>
          <van-button @click="importHolidays">
            <van-icon name="download" /> 导入节假日
          </van-button>
        </div>
        
        <div class="holiday-list" v-if="holidays.length > 0">
          <div 
            v-for="holiday in holidays" 
            :key="holiday.id"
            class="holiday-item card"
          >
            <div class="holiday-info">
              <div class="holiday-name">{{ holiday.name }}</div>
              <div class="holiday-date">{{ formatDate(holiday.date) }}</div>
              <div class="holiday-type">
                <van-tag :type="getHolidayTypeTagType(holiday.type)">
                  {{ getHolidayTypeText(holiday.type) }}
                </van-tag>
              </div>
            </div>
            <div class="holiday-actions">
              <van-button size="small" type="primary" @click="editHoliday(holiday)">
                编辑
              </van-button>
              <van-button size="small" type="danger" @click="deleteHoliday(holiday.id)">
                删除
              </van-button>
            </div>
          </div>
        </div>
        
        <van-empty v-else description="暂无节假日记录" />
      </div>
    </div>
    
    <!-- 添加/编辑寒暑假弹窗 -->
    <van-popup v-model:show="showVacationForm" position="bottom" :style="{ height: '80%' }">
      <div class="vacation-form-popup">
        <div class="popup-header">
          <h3>{{ editingVacation ? '编辑寒暑假' : '添加寒暑假' }}</h3>
          <van-icon name="cross" @click="closeVacationForm" />
        </div>
        
        <van-form @submit="submitVacationForm" ref="vacationFormRef">
          <van-field
            v-model="vacationForm.name"
            name="name"
            label="假期名称"
            placeholder="请输入假期名称（如：2024年寒假）"
            :rules="[{ required: true, message: '请输入假期名称' }]"
          />
          
          <van-cell-group>
            <van-cell title="开始日期" is-link @click="showVacationStartDatePicker = true">
              <template #default>
                <span v-if="vacationForm.startDate">{{ formatDate(vacationForm.startDate) }}</span>
                <span v-else class="placeholder">请选择开始日期</span>
              </template>
            </van-cell>
            
            <van-cell title="结束日期" is-link @click="showVacationEndDatePicker = true">
              <template #default>
                <span v-if="vacationForm.endDate">{{ formatDate(vacationForm.endDate) }}</span>
                <span v-else class="placeholder">请选择结束日期</span>
              </template>
            </van-cell>
          </van-cell-group>
          
          <van-field
            v-model="vacationForm.description"
            name="description"
            label="假期描述"
            type="textarea"
            rows="3"
            placeholder="请输入假期描述（选填）"
          />
          
          <div class="form-buttons">
            <van-button block @click="closeVacationForm">取消</van-button>
            <van-button block type="primary" native-type="submit" :loading="submitting">
              保存
            </van-button>
          </div>
        </van-form>
      </div>
    </van-popup>
    
    <!-- 开始日期选择器 -->
    <van-popup v-model:show="showVacationStartDatePicker" position="bottom">
      <van-date-picker
        v-model="vacationStartDate"
        title="选择开始日期"
        @confirm="onVacationStartDateConfirm"
        @cancel="showVacationStartDatePicker = false"
      />
    </van-popup>
    
    <!-- 结束日期选择器 -->
    <van-popup v-model:show="showVacationEndDatePicker" position="bottom">
      <van-date-picker
        v-model="vacationEndDate"
        title="选择结束日期"
        @confirm="onVacationEndDateConfirm"
        @cancel="showVacationEndDatePicker = false"
      />
    </van-popup>
    
    <!-- 添加/编辑节假日弹窗 -->
    <van-popup v-model:show="showHolidayForm" position="bottom" :style="{ height: '70%' }">
      <div class="holiday-form-popup">
        <div class="popup-header">
          <h3>{{ editingHoliday ? '编辑节假日' : '添加节假日' }}</h3>
          <van-icon name="cross" @click="closeHolidayForm" />
        </div>
        
        <van-form @submit="submitHolidayForm" ref="holidayFormRef">
          <van-field
            v-model="holidayForm.name"
            name="name"
            label="节假日名称"
            placeholder="请输入节假日名称（如：元旦）"
            :rules="[{ required: true, message: '请输入节假日名称' }]"
          />
          
          <van-cell-group>
            <van-cell title="日期" is-link @click="showHolidayDatePicker = true">
              <template #default>
                <span v-if="holidayForm.date">{{ formatDate(holidayForm.date) }}</span>
                <span v-else class="placeholder">请选择日期</span>
              </template>
            </van-cell>
          </van-cell-group>
          
          <van-field
            name="type"
            label="类型"
            value=""
          >
            <template #input>
              <van-radio-group v-model="holidayForm.type" direction="horizontal">
                <van-radio name="holiday">节假日</van-radio>
                <van-radio name="workday">调休工作日</van-radio>
              </van-radio-group>
            </template>
          </van-field>
          
          <div class="form-buttons">
            <van-button block @click="closeHolidayForm">取消</van-button>
            <van-button block type="primary" native-type="submit" :loading="submitting">
              保存
            </van-button>
          </div>
        </van-form>
      </div>
    </van-popup>
    
    <!-- 节假日日期选择器 -->
    <van-popup v-model:show="showHolidayDatePicker" position="bottom">
      <van-date-picker
        v-model="holidayDate"
        title="选择日期"
        @confirm="onHolidayDateConfirm"
        @cancel="showHolidayDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { showToast, showConfirmDialog } from 'vant'
import dayjs from 'dayjs'
import { useBusStore } from '../stores/bus.js'

const busStore = useBusStore()

// 响应式数据
const activeTab = ref('vacation')
const vacations = ref([])
const holidays = ref([])
const submitting = ref(false)

// 寒暑假表单相关
const showVacationForm = ref(false)
const editingVacation = ref(null)
const vacationForm = ref({
  name: '',
  startDate: '',
  endDate: '',
  description: ''
})
const showVacationStartDatePicker = ref(false)
const showVacationEndDatePicker = ref(false)
const vacationStartDate = ref([])
const vacationEndDate = ref([])
const vacationFormRef = ref(null)

// 节假日表单相关
const showHolidayForm = ref(false)
const editingHoliday = ref(null)
const holidayForm = ref({
  name: '',
  date: '',
  type: 'holiday' // holiday: 节假日, workday: 调休工作日
})
const showHolidayDatePicker = ref(false)
const holidayDate = ref([])
const holidayFormRef = ref(null)

// 初始化数据
onMounted(() => {
  loadVacations()
  loadHolidays()
})

// 加载寒暑假数据
const loadVacations = async () => {
  try {
    // 从bus store获取数据
    vacations.value = busStore.summerVacations.concat(busStore.winterVacations)
  } catch (error) {
    showToast('加载寒暑假数据失败')
    console.error('加载寒暑假数据失败:', error)
  }
}

// 加载节假日数据
const loadHolidays = async () => {
  try {
    // 从bus store获取数据
    holidays.value = [...busStore.holidays]
  } catch (error) {
    showToast('加载节假日数据失败')
    console.error('加载节假日数据失败:', error)
  }
}

// 标签切换
const onTabChange = (tab) => {
  activeTab.value = tab
}

// 显示添加寒暑假面板
const showAddVacationPanel = () => {
  editingVacation.value = null
  resetVacationForm()
  showVacationForm.value = true
}

// 编辑寒暑假
const editVacation = (vacation) => {
  editingVacation.value = vacation
  vacationForm.value = {
    ...vacation
  }
  
  // 设置日期选择器的初始值
  const start = dayjs(vacation.startDate)
  const end = dayjs(vacation.endDate)
  vacationStartDate.value = [start.year(), start.month() + 1, start.date()]
  vacationEndDate.value = [end.year(), end.month() + 1, end.date()]
  
  showVacationForm.value = true
}

// 关闭寒暑假表单
const closeVacationForm = () => {
  showVacationForm.value = false
  resetVacationForm()
}

// 重置寒暑假表单
const resetVacationForm = () => {
  vacationForm.value = {
    name: '',
    startDate: '',
    endDate: '',
    description: ''
  }
  vacationStartDate.value = []
  vacationEndDate.value = []
  if (vacationFormRef.value) {
    vacationFormRef.value.resetValidation()
  }
}

// 提交寒暑假表单
const submitVacationForm = async () => {
  if (!vacationForm.value.startDate || !vacationForm.value.endDate) {
    showToast('请选择开始日期和结束日期')
    return
  }
  
  if (dayjs(vacationForm.value.endDate).isBefore(dayjs(vacationForm.value.startDate))) {
    showToast('结束日期不能早于开始日期')
    return
  }
  
  submitting.value = true
  try {
    // 调用bus store中的方法保存数据
    if (editingVacation.value) {
      // 更新现有记录
      if (editingVacation.value.type === 'summer') {
        await busStore.updateSummerVacation({
          ...vacationForm.value,
          id: editingVacation.value.id,
          type: 'summer'
        })
      } else {
        await busStore.updateWinterVacation({
          ...vacationForm.value,
          id: editingVacation.value.id,
          type: 'winter'
        })
      }
      showToast('寒暑假更新成功')
    } else {
      // 添加新记录
      const type = vacationForm.value.name.includes('暑假') ? 'summer' : 'winter'
      await busStore.addVacation({
        ...vacationForm.value,
        type: type,
        id: Date.now().toString()
      })
      showToast('寒暑假添加成功')
    }
    
    closeVacationForm()
    // 重新加载数据
    await loadVacations()
  } catch (error) {
    showToast(editingVacation.value ? '寒暑假更新失败' : '寒暑假添加失败')
    console.error('保存寒暑假数据失败:', error)
  } finally {
    submitting.value = false
  }
}

// 删除寒暑假
const deleteVacation = async (id, type) => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '确定要删除这个寒暑假记录吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })
    
    // 调用bus store中的方法删除数据
    if (type === 'summer') {
      await busStore.deleteSummerVacation(id)
    } else {
      await busStore.deleteWinterVacation(id)
    }
    
    // 重新加载数据
    await loadVacations()
    showToast('寒暑假删除成功')
  } catch (error) {
    // 用户取消操作也会进入catch，但不需要提示
    if (error !== 'cancel') {
      showToast('寒暑假删除失败')
      console.error('删除寒暑假数据失败:', error)
    }
  }
}

// 显示添加节假日面板
const showAddHolidayPanel = () => {
  editingHoliday.value = null
  resetHolidayForm()
  showHolidayForm.value = true
}

// 编辑节假日
const editHoliday = (holiday) => {
  editingHoliday.value = holiday
  holidayForm.value = {
    ...holiday
  }
  
  // 设置日期选择器的初始值
  const date = dayjs(holiday.date)
  holidayDate.value = [date.year(), date.month() + 1, date.date()]
  
  showHolidayForm.value = true
}

// 关闭节假日表单
const closeHolidayForm = () => {
  showHolidayForm.value = false
  resetHolidayForm()
}

// 重置节假日表单
const resetHolidayForm = () => {
  holidayForm.value = {
    name: '',
    date: '',
    type: 'holiday'
  }
  holidayDate.value = []
  if (holidayFormRef.value) {
    holidayFormRef.value.resetValidation()
  }
}

// 提交节假日表单
const submitHolidayForm = async () => {
  if (!holidayForm.value.date) {
    showToast('请选择日期')
    return
  }
  
  submitting.value = true
  try {
    // 调用bus store中的方法保存数据
    if (editingHoliday.value) {
      // 更新现有记录
      await busStore.updateHoliday({
        ...holidayForm.value,
        id: editingHoliday.value.id
      })
      showToast('节假日更新成功')
    } else {
      // 添加新记录
      await busStore.addHoliday({
        ...holidayForm.value,
        id: Date.now().toString()
      })
      showToast('节假日添加成功')
    }
    
    closeHolidayForm()
    // 重新加载数据
    await loadHolidays()
  } catch (error) {
    showToast(editingHoliday.value ? '节假日更新失败' : '节假日添加失败')
    console.error('保存节假日数据失败:', error)
  } finally {
    submitting.value = false
  }
}

// 删除节假日
const deleteHoliday = async (id) => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '确定要删除这个节假日记录吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })
    
    // 调用bus store中的方法删除数据
    await busStore.deleteHoliday(id)
    
    // 重新加载数据
    await loadHolidays()
    showToast('节假日删除成功')
  } catch (error) {
    // 用户取消操作也会进入catch，但不需要提示
    if (error !== 'cancel') {
      showToast('节假日删除失败')
      console.error('删除节假日数据失败:', error)
    }
  }
}

// 导入节假日
const importHolidays = () => {
  // 实际项目中应该实现导入功能
  showToast('导入功能开发中...')
}

// 日期选择器确认事件
const onVacationStartDateConfirm = () => {
  vacationForm.value.startDate = dayjs(vacationStartDate.value).format('YYYY-MM-DD')
  showVacationStartDatePicker.value = false
}

const onVacationEndDateConfirm = () => {
  vacationForm.value.endDate = dayjs(vacationEndDate.value).format('YYYY-MM-DD')
  showVacationEndDatePicker.value = false
}

const onHolidayDateConfirm = () => {
  holidayForm.value.date = dayjs(holidayDate.value).format('YYYY-MM-DD')
  showHolidayDatePicker.value = false
}

// 格式化日期
const formatDate = (date) => {
  return dayjs(date).format('YYYY年MM月DD日')
}

// 获取节假日类型文本
const getHolidayTypeText = (type) => {
  return type === 'holiday' ? '节假日' : '调休工作日'
}

// 获取节假日类型标签样式
const getHolidayTypeTagType = (type) => {
  return type === 'holiday' ? 'primary' : 'warning'
}
</script>

<style scoped>
.vacation-management-page {
  width: 100%;
  min-height: 100vh;
  background-color: #f7f8fa;
  overflow-x: hidden;
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 20px;
  gap: 8px;
}

.tab-container {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.button-group {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.vacation-content,
.holiday-content {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.vacation-list,
.holiday-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.vacation-item,
.holiday-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.vacation-info,
.holiday-info {
  flex: 1;
}

.vacation-name,
.holiday-name {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.vacation-period,
.holiday-date {
  font-size: 14px;
  color: #646566;
  margin-bottom: 4px;
}

.vacation-desc {
  font-size: 14px;
  color: #969799;
}

.vacation-actions,
.holiday-actions {
  display: flex;
  gap: 8px;
}

.holiday-type {
  margin-top: 4px;
}

/* 弹窗样式 */
.vacation-form-popup,
.holiday-form-popup {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebedf0;
}

.popup-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.placeholder {
  color: #969799;
}

.form-buttons {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.card {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
}
</style>