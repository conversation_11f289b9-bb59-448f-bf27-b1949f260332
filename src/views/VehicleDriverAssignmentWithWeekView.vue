<template>
  <div class="vehicle-driver-assignment-page">
    <div class="page-container">
      <div class="section-title">
        <van-icon name="car" />
        班次管理
      </div>
      
      <!-- 标签页切换 -->
      <van-tabs v-model:active="activeTab" @change="onTabChange" class="tab-container">
        <van-tab title="星期视图" name="weekView"></van-tab>
        <van-tab title="假期管理" name="vacationManagement"></van-tab>
      </van-tabs>
      
      <!-- 星期视图 -->
      <div v-if="activeTab === 'weekView'" class="week-view-content">
        <!-- 周选择器 -->
        <div class="week-selector card">
          <div class="week-navigation">
            <van-button size="small" @click="previousWeek">上一周</van-button>
            <div class="current-week-text">{{ currentWeekText }}</div>
            <van-button size="small" @click="nextWeek">下一周</van-button>
          </div>
          
          <!-- 星期视图 -->
          <div class="week-calendar">
            <div 
              v-for="day in weekDays" 
              :key="day.date"
              class="week-day"
              :class="{ 'today': day.isToday, 'has-assignments': day.hasAssignments, 'selected': selectedDateText === day.formattedDate }"
              @click="selectDate(day)"
            >
              <div class="day-header">
                <div class="day-name">{{ day.dayName }}</div>
                <div class="day-number">{{ day.dayNumber }}</div>
                <div v-if="day.isToday" class="today-tag">今天</div>
              </div>
              <div v-if="day.isHoliday" class="holiday-tag">{{ day.holidayName }}</div>
              <div class="day-status">
                <div v-if="day.hasAssignments" class="status-dot assigned"></div>
                <div v-else-if="day.hasSchedules" class="status-dot unassigned"></div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 选中日期的班次安排 -->
        <div v-if="selectedDateText" class="assignment-content card">
          <div class="date-title">{{ selectedDateText }} - 班次安排</div>
          
          <div v-if="loading" class="loading-container">
            <van-loading type="spinner" color="#1989fa" />
            <span>加载中...</span>
          </div>
          
          <div v-else-if="dateSchedules.length > 0" class="schedule-list">
            <div 
              v-for="schedule in dateSchedules" 
              :key="schedule.id"
              class="schedule-item"
            >
              <div class="schedule-header">
                <div class="schedule-info">
                  <div class="schedule-time">{{ schedule.time }}</div>
                  <div class="schedule-route">{{ schedule.route }}</div>
                </div>
                <div class="schedule-stations">
                  <van-tag 
                    v-for="station in schedule.stations" 
                    :key="station"
                    size="mini"
                    type="primary"
                    plain
                  >
                    {{ station }}
                  </van-tag>
                </div>
              </div>
              
              <div class="assignment-form">
                <van-cell-group>
                  <van-field
                    v-model="schedule.vehicleNumber"
                    name="vehicleNumber"
                    label="车牌号"
                    placeholder="请输入车牌号"
                  />
                  <van-field
                    v-model="schedule.driverName"
                    name="driverName"
                    label="驾驶员"
                    placeholder="请选择驾驶员"
                    readonly
                    is-link
                    @click="showDriverSelector = true; selectedScheduleForDriver = schedule"
                  />
                </van-cell-group>
                
                <div class="action-buttons">
                  <van-button size="small" type="primary" @click="saveAssignment(schedule)">
                    保存安排
                  </van-button>
                </div>
              </div>
            </div>
          </div>
          
          <van-empty v-else description="该日期暂无班次" />
        </div>
      </div>
      
      <!-- 假期管理 -->
      <div v-if="activeTab === 'vacationManagement'" class="vacation-management-content">
        <!-- 假期类型选择 -->
        <van-tabs v-model:active="vacationActiveTab" @change="onVacationTabChange" class="vacation-tab-container">
          <van-tab title="寒暑假管理" name="vacation"></van-tab>
          <van-tab title="节假日管理" name="holiday"></van-tab>
        </van-tabs>
        
        <!-- 寒暑假管理 -->
        <div v-if="vacationActiveTab === 'vacation'" class="vacation-content">
          <div class="button-group">
            <van-button type="primary" @click="showAddVacationPanel">
              <van-icon name="plus" /> 添加寒暑假
            </van-button>
          </div>
          
          <div class="vacation-list" v-if="vacations.length > 0">
            <div 
              v-for="vacation in vacations" 
              :key="vacation.id"
              class="vacation-item card"
            >
              <div class="vacation-info">
                <div class="vacation-name">{{ vacation.name }}</div>
                <div class="vacation-period">
                  {{ formatDate(vacation.startDate) }} 至 {{ formatDate(vacation.endDate) }}
                </div>
                <div class="vacation-desc">{{ vacation.description }}</div>
              </div>
              <div class="vacation-actions">
                <van-button size="small" type="primary" @click="editVacation(vacation)">
                  编辑
                </van-button>
                <van-button size="small" type="danger" @click="deleteVacation(vacation.id, vacation.type)">
                  删除
                </van-button>
              </div>
            </div>
          </div>
          
          <van-empty v-else description="暂无寒暑假记录" />
        </div>
        
        <!-- 节假日管理 -->
        <div v-if="vacationActiveTab === 'holiday'" class="holiday-content">
          <div class="button-group">
            <van-button type="primary" @click="showAddHolidayPanel">
              <van-icon name="plus" /> 添加节假日
            </van-button>
            <van-button @click="importHolidays">
              <van-icon name="download" /> 导入节假日
            </van-button>
          </div>
          
          <div class="holiday-list" v-if="holidays.length > 0">
            <div 
              v-for="holiday in holidays" 
              :key="holiday.id"
              class="holiday-item card"
            >
              <div class="holiday-info">
                <div class="holiday-name">{{ holiday.name }}</div>
                <div class="holiday-date">{{ formatDate(holiday.date) }}</div>
                <div class="holiday-type">
                  <van-tag :type="getHolidayTypeTagType(holiday.type)">
                    {{ getHolidayTypeText(holiday.type) }}
                  </van-tag>
                </div>
              </div>
              <div class="holiday-actions">
                <van-button size="small" type="primary" @click="editHoliday(holiday)">
                  编辑
                </van-button>
                <van-button size="small" type="danger" @click="deleteHoliday(holiday.id)">
                  删除
                </van-button>
              </div>
            </div>
          </div>
          
          <van-empty v-else description="暂无节假日记录" />
        </div>
      </div>
    </div>
    
    <!-- 驾驶员选择器 -->
    <van-popup v-model:show="showDriverSelector" position="bottom" :style="{ height: '70%' }">
      <div class="selector-popup">
        <div class="popup-header">
          <h3>选择驾驶员</h3>
          <van-icon name="cross" @click="showDriverSelector = false" />
        </div>
        
        <div class="selector-list">
          <div 
            v-for="driver in driverUsers"
            :key="driver.id"
            class="selector-item"
            :class="{ 'selected': selectedScheduleForDriver?.driverId === driver.id }"
            @click="selectDriver(driver)"
          >
            <div class="selector-item-info">
              <div class="selector-item-title">{{ driver.name }}</div>
              <div class="selector-item-desc">{{ driver.phone }}</div>
            </div>
            <van-icon name="success" v-if="selectedScheduleForDriver?.driverId === driver.id" class="selected-icon" />
          </div>
          <van-empty 
            v-if="driverUsers.length === 0"
            description="暂无驾驶员数据"
          />
        </div>
      </div>
    </van-popup>
    
    <!-- 添加/编辑寒暑假弹窗 -->
    <van-popup v-model:show="showVacationForm" position="bottom" :style="{ height: '80%' }">
      <div class="vacation-form-popup">
        <div class="popup-header">
          <h3>{{ editingVacation ? '编辑寒暑假' : '添加寒暑假' }}</h3>
          <van-icon name="cross" @click="closeVacationForm" />
        </div>
        
        <van-form @submit="submitVacationForm" ref="vacationFormRef">
          <van-field
            v-model="vacationForm.name"
            name="name"
            label="假期名称"
            placeholder="请输入假期名称（如：2024年寒假）"
            :rules="[{ required: true, message: '请输入假期名称' }]"
          />
          
          <van-cell-group>
            <van-cell title="开始日期" is-link @click="showVacationStartDatePicker = true">
              <template #default>
                <span v-if="vacationForm.startDate">{{ formatDate(vacationForm.startDate) }}</span>
                <span v-else class="placeholder">请选择开始日期</span>
              </template>
            </van-cell>
            
            <van-cell title="结束日期" is-link @click="showVacationEndDatePicker = true">
              <template #default>
                <span v-if="vacationForm.endDate">{{ formatDate(vacationForm.endDate) }}</span>
                <span v-else class="placeholder">请选择结束日期</span>
              </template>
            </van-cell>
          </van-cell-group>
          
          <van-field
            v-model="vacationForm.description"
            name="description"
            label="假期描述"
            type="textarea"
            rows="3"
            placeholder="请输入假期描述（选填）"
          />
          
          <div class="form-buttons">
            <van-button block @click="closeVacationForm">取消</van-button>
            <van-button block type="primary" native-type="submit" :loading="submitting">
              保存
            </van-button>
          </div>
        </van-form>
      </div>
    </van-popup>
    
    <!-- 开始日期选择器 -->
    <van-popup v-model:show="showVacationStartDatePicker" position="bottom">
      <van-date-picker
        v-model="vacationStartDate"
        title="选择开始日期"
        @confirm="onVacationStartDateConfirm"
        @cancel="showVacationStartDatePicker = false"
      />
    </van-popup>
    
    <!-- 结束日期选择器 -->
    <van-popup v-model:show="showVacationEndDatePicker" position="bottom">
      <van-date-picker
        v-model="vacationEndDate"
        title="选择结束日期"
        @confirm="onVacationEndDateConfirm"
        @cancel="showVacationEndDatePicker = false"
      />
    </van-popup>
    
    <!-- 添加/编辑节假日弹窗 -->
    <van-popup v-model:show="showHolidayForm" position="bottom" :style="{ height: '70%' }">
      <div class="holiday-form-popup">
        <div class="popup-header">
          <h3>{{ editingHoliday ? '编辑节假日' : '添加节假日' }}</h3>
          <van-icon name="cross" @click="closeHolidayForm" />
        </div>
        
        <van-form @submit="submitHolidayForm" ref="holidayFormRef">
          <van-field
            v-model="holidayForm.name"
            name="name"
            label="节假日名称"
            placeholder="请输入节假日名称（如：元旦）"
            :rules="[{ required: true, message: '请输入节假日名称' }]"
          />
          
          <van-cell-group>
            <van-cell title="日期" is-link @click="showHolidayDatePicker = true">
              <template #default>
                <span v-if="holidayForm.date">{{ formatDate(holidayForm.date) }}</span>
                <span v-else class="placeholder">请选择日期</span>
              </template>
            </van-cell>
          </van-cell-group>
          
          <van-field
            name="type"
            label="类型"
            value=""
          >
            <template #input>
              <van-radio-group v-model="holidayForm.type" direction="horizontal">
                <van-radio name="holiday">节假日</van-radio>
                <van-radio name="workday">调休工作日</van-radio>
              </van-radio-group>
            </template>
          </van-field>
          
          <div class="form-buttons">
            <van-button block @click="closeHolidayForm">取消</van-button>
            <van-button block type="primary" native-type="submit" :loading="submitting">
              保存
            </van-button>
          </div>
        </van-form>
      </div>
    </van-popup>
    
    <!-- 节假日日期选择器 -->
    <van-popup v-model:show="showHolidayDatePicker" position="bottom">
      <van-date-picker
        v-model="holidayDate"
        title="选择日期"
        @confirm="onHolidayDateConfirm"
        @cancel="showHolidayDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { showToast, showConfirmDialog } from 'vant'
import dayjs from 'dayjs'
import { useBusStore } from '../stores/bus.js'

const busStore = useBusStore()

// 标签页控制
const activeTab = ref('weekView') // weekView 或 vacationManagement
const vacationActiveTab = ref('vacation') // vacation 或 holiday

// 星期视图相关数据
const loading = ref(false)
const submitting = ref(false)
const selectedDateText = ref('')
const selectedDate = ref([])
const dateSchedules = ref([])
const currentWeekStart = ref(dayjs().startOf('week'))
const weekDays = ref([])

// 驾驶员数据
const driverUsers = ref([
  { id: 'driver1', name: '驾驶员1', phone: '13800138006', type: 'teacher', department: '车队' },
  { id: 'driver2', name: '驾驶员2', phone: '13800138007', type: 'teacher', department: '车队' },
  { id: 'driver3', name: '驾驶员3', phone: '13800138008', type: 'teacher', department: '车队' }
])

// 假期管理相关数据
const vacations = ref([])
const holidays = ref([])

// 弹窗控制
const showDriverSelector = ref(false)
const showVacationForm = ref(false)
const showHolidayForm = ref(false)
const showVacationStartDatePicker = ref(false)
const showVacationEndDatePicker = ref(false)
const showHolidayDatePicker = ref(false)

// 表单数据
const selectedScheduleForDriver = ref(null)
const editingVacation = ref(null)
const editingHoliday = ref(null)
const vacationForm = ref({
  name: '',
  startDate: '',
  endDate: '',
  description: ''
})
const holidayForm = ref({
  name: '',
  date: '',
  type: 'holiday' // holiday: 节假日, workday: 调休工作日
})
const vacationStartDate = ref([])
const vacationEndDate = ref([])
const holidayDate = ref([])
const vacationFormRef = ref(null)
const holidayFormRef = ref(null)

// 计算属性：当前周文本
const currentWeekText = computed(() => {
  const start = currentWeekStart.value
  const end = currentWeekStart.value.add(6, 'day')
  return `${start.format('YYYY年MM月DD日')} - ${end.format('YYYY年MM月DD日')}`
})

// 初始化数据
onMounted(() => {
  initWeekDays()
  loadVacations()
  loadHolidays()
})

// 初始化星期数据
const initWeekDays = async () => {
  loading.value = true
  try {
    const days = []
    const today = dayjs()
    
    // 获取所有假期数据
    await loadHolidays()
    
    for (let i = 0; i < 7; i++) {
      const date = currentWeekStart.value.add(i, 'day')
      const dateStr = date.format('YYYY-MM-DD')
      const isToday = date.isSame(today, 'day')
      
      // 检查是否是节假日
      const holiday = holidays.value.find(h => h.date === dateStr)
      
      // 检查是否有班次
      const schedules = busStore.getScheduleByDate(dateStr)
      const hasSchedules = schedules.length > 0
      
      // 检查是否有已安排的班次
      const assignments = busStore.getAssignmentsByDate(dateStr)
      const hasAssignments = assignments.length > 0
      
      days.push({
        date: dateStr,
        formattedDate: date.format('YYYY年MM月DD日'),
        dayName: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.day()],
        dayNumber: date.date(),
        isToday,
        isHoliday: !!holiday,
        holidayName: holiday?.name || '',
        holidayType: holiday?.type || '',
        hasSchedules,
        hasAssignments
      })
    }
    
    weekDays.value = days
    
    // 默认选中今天或本周第一天
    const todayIndex = days.findIndex(day => day.isToday)
    if (todayIndex !== -1) {
      selectDate(days[todayIndex])
    } else {
      selectDate(days[0])
    }
  } catch (error) {
    showToast('加载星期数据失败')
    console.error('加载星期数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 上一周
const previousWeek = () => {
  currentWeekStart.value = currentWeekStart.value.subtract(1, 'week')
  initWeekDays()
}

// 下一周
const nextWeek = () => {
  currentWeekStart.value = currentWeekStart.value.add(1, 'week')
  initWeekDays()
}

// 选择日期
const selectDate = (day) => {
  selectedDateText.value = day.formattedDate
  selectedDate.value = [dayjs(day.date).year(), dayjs(day.date).month() + 1, dayjs(day.date).date()]
  loadDateSchedules()
}

// 加载指定日期的班次
const loadDateSchedules = async () => {
  if (!selectedDate.value || selectedDate.value.length === 0) {
    return
  }
  
  loading.value = true
  try {
    const dateStr = dayjs(selectedDate.value).format('YYYY-MM-DD')
    // 从store获取班次
    const schedules = busStore.getScheduleByDate(dateStr)
    
    // 从store获取已有的安排信息
    const assignments = busStore.getAssignmentsByDate(dateStr)
    
    // 为每个班次添加车牌号和驾驶员字段
    dateSchedules.value = schedules.map(schedule => {
      const assignment = assignments.find(a => a.scheduleId === schedule.id)
      return {
        ...schedule,
        vehicleNumber: assignment?.vehicleNumber || '',
        driverId: assignment?.driverId || '',
        driverName: assignment?.driverName || ''
      }
    })
  } catch (error) {
    showToast('加载班次信息失败')
    console.error('加载班次信息失败:', error)
  } finally {
    loading.value = false
  }
}

// 选择驾驶员
const selectDriver = (driver) => {
  if (selectedScheduleForDriver.value) {
    selectedScheduleForDriver.value.driverId = driver.id
    selectedScheduleForDriver.value.driverName = driver.name
  }
  showDriverSelector.value = false
}

// 保存班次安排
const saveAssignment = async (schedule) => {
  if (!schedule.vehicleNumber.trim()) {
    showToast('请输入车牌号')
    return
  }
  
  if (!schedule.driverId) {
    showToast('请选择驾驶员')
    return
  }
  
  submitting.value = true
  try {
    // 调用bus store中的方法保存数据
    await busStore.saveScheduleAssignment({
      date: dayjs(selectedDate.value).format('YYYY-MM-DD'),
      scheduleId: schedule.id,
      vehicleNumber: schedule.vehicleNumber,
      driverId: schedule.driverId,
      driverName: schedule.driverName
    })
    
    showToast('班次安排保存成功')
    // 更新星期视图中的状态
    await initWeekDays()
  } catch (error) {
    showToast('班次安排保存失败')
    console.error('保存班次安排失败:', error)
  } finally {
    submitting.value = false
  }
}

// 标签页切换
const onTabChange = (tab) => {
  activeTab.value = tab
  if (tab === 'vacationManagement') {
    loadVacations()
    loadHolidays()
  } else if (tab === 'weekView') {
    initWeekDays()
  }
}

// 假期管理标签页切换
const onVacationTabChange = (tab) => {
  vacationActiveTab.value = tab
}

// 加载寒暑假数据
const loadVacations = async () => {
  try {
    // 从bus store获取数据
    vacations.value = busStore.summerVacations.concat(busStore.winterVacations)
  } catch (error) {
    showToast('加载寒暑假数据失败')
    console.error('加载寒暑假数据失败:', error)
  }
}

// 加载节假日数据
const loadHolidays = async () => {
  try {
    // 从bus store获取数据
    holidays.value = [...busStore.holidays]
  } catch (error) {
    showToast('加载节假日数据失败')
    console.error('加载节假日数据失败:', error)
  }
}

// 显示添加寒暑假面板
const showAddVacationPanel = () => {
  editingVacation.value = null
  resetVacationForm()
  showVacationForm.value = true
}

// 编辑寒暑假
const editVacation = (vacation) => {
  editingVacation.value = vacation
  vacationForm.value = {
    ...vacation
  }
  
  // 设置日期选择器的初始值
  const start = dayjs(vacation.startDate)
  const end = dayjs(vacation.endDate)
  vacationStartDate.value = [start.year(), start.month() + 1, start.date()]
  vacationEndDate.value = [end.year(), end.month() + 1, end.date()]
  
  showVacationForm.value = true
}

// 关闭寒暑假表单
const closeVacationForm = () => {
  showVacationForm.value = false
  resetVacationForm()
}

// 重置寒暑假表单
const resetVacationForm = () => {
  vacationForm.value = {
    name: '',
    startDate: '',
    endDate: '',
    description: ''
  }
  vacationStartDate.value = []
  vacationEndDate.value = []
  if (vacationFormRef.value) {
    vacationFormRef.value.resetValidation()
  }
}

// 提交寒暑假表单
const submitVacationForm = async () => {
  if (!vacationForm.value.startDate || !vacationForm.value.endDate) {
    showToast('请选择开始日期和结束日期')
    return
  }
  
  if (dayjs(vacationForm.value.endDate).isBefore(dayjs(vacationForm.value.startDate))) {
    showToast('结束日期不能早于开始日期')
    return
  }
  
  submitting.value = true
  try {
    // 调用bus store中的方法保存数据
    if (editingVacation.value) {
      // 更新现有记录
      if (editingVacation.value.type === 'summer') {
        await busStore.updateSummerVacation({
          ...vacationForm.value,
          id: editingVacation.value.id,
          type: 'summer'
        })
      } else {
        await busStore.updateWinterVacation({
          ...vacationForm.value,
          id: editingVacation.value.id,
          type: 'winter'
        })
      }
      showToast('寒暑假更新成功')
    } else {
      // 添加新记录
      const type = vacationForm.value.name.includes('暑假') ? 'summer' : 'winter'
      await busStore.addVacation({
        ...vacationForm.value,
        type: type,
        id: Date.now().toString()
      })
      showToast('寒暑假添加成功')
    }
    
    closeVacationForm()
    // 重新加载数据
    await loadVacations()
  } catch (error) {
    showToast(editingVacation.value ? '寒暑假更新失败' : '寒暑假添加失败')
    console.error('保存寒暑假数据失败:', error)
  } finally {
    submitting.value = false
  }
}

// 删除寒暑假
const deleteVacation = async (id, type) => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '确定要删除这个寒暑假记录吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })
    
    // 调用bus store中的方法删除数据
    if (type === 'summer') {
      await busStore.deleteSummerVacation(id)
    } else {
      await busStore.deleteWinterVacation(id)
    }
    
    // 重新加载数据
    await loadVacations()
    showToast('寒暑假删除成功')
  } catch (error) {
    // 用户取消操作也会进入catch，但不需要提示
    if (error !== 'cancel') {
      showToast('寒暑假删除失败')
      console.error('删除寒暑假数据失败:', error)
    }
  }
}

// 显示添加节假日面板
const showAddHolidayPanel = () => {
  editingHoliday.value = null
  resetHolidayForm()
  showHolidayForm.value = true
}

// 编辑节假日
const editHoliday = (holiday) => {
  editingHoliday.value = holiday
  holidayForm.value = {
    ...holiday
  }
  
  // 设置日期选择器的初始值
  const date = dayjs(holiday.date)
  holidayDate.value = [date.year(), date.month() + 1, date.date()]
  
  showHolidayForm.value = true
}

// 关闭节假日表单
const closeHolidayForm = () => {
  showHolidayForm.value = false
  resetHolidayForm()
}

// 重置节假日表单
const resetHolidayForm = () => {
  holidayForm.value = {
    name: '',
    date: '',
    type: 'holiday'
  }
  holidayDate.value = []
  if (holidayFormRef.value) {
    holidayFormRef.value.resetValidation()
  }
}

// 提交节假日表单
const submitHolidayForm = async () => {
  if (!holidayForm.value.date) {
    showToast('请选择日期')
    return
  }
  
  submitting.value = true
  try {
    // 调用bus store中的方法保存数据
    if (editingHoliday.value) {
      // 更新现有记录
      await busStore.updateHoliday({
        ...holidayForm.value,
        id: editingHoliday.value.id
      })
      showToast('节假日更新成功')
    } else {
      // 添加新记录
      await busStore.addHoliday({
        ...holidayForm.value,
        id: Date.now().toString()
      })
      showToast('节假日添加成功')
    }
    
    closeHolidayForm()
    // 重新加载数据
    await loadHolidays()
    // 更新星期视图中的节假日信息
    initWeekDays()
  } catch (error) {
    showToast(editingHoliday.value ? '节假日更新失败' : '节假日添加失败')
    console.error('保存节假日数据失败:', error)
  } finally {
    submitting.value = false
  }
}

// 删除节假日
const deleteHoliday = async (id) => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '确定要删除这个节假日记录吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })
    
    // 调用bus store中的方法删除数据
    await busStore.deleteHoliday(id)
    
    // 重新加载数据
    await loadHolidays()
    // 更新星期视图中的节假日信息
    initWeekDays()
    showToast('节假日删除成功')
  } catch (error) {
    // 用户取消操作也会进入catch，但不需要提示
    if (error !== 'cancel') {
      showToast('节假日删除失败')
      console.error('删除节假日数据失败:', error)
    }
  }
}

// 导入节假日
const importHolidays = () => {
  // 实际项目中应该实现导入功能
  showToast('导入功能开发中...')
}

// 日期选择器确认事件
const onVacationStartDateConfirm = () => {
  vacationForm.value.startDate = dayjs(vacationStartDate.value).format('YYYY-MM-DD')
  showVacationStartDatePicker.value = false
}

const onVacationEndDateConfirm = () => {
  vacationForm.value.endDate = dayjs(vacationEndDate.value).format('YYYY-MM-DD')
  showVacationEndDatePicker.value = false
}

const onHolidayDateConfirm = () => {
  holidayForm.value.date = dayjs(holidayDate.value).format('YYYY-MM-DD')
  showHolidayDatePicker.value = false
}

// 格式化日期
const formatDate = (date) => {
  return dayjs(date).format('YYYY年MM月DD日')
}

// 获取节假日类型文本
const getHolidayTypeText = (type) => {
  return type === 'holiday' ? '节假日' : '调休工作日'
}

// 获取节假日类型标签样式
const getHolidayTypeTagType = (type) => {
  return type === 'holiday' ? 'primary' : 'warning'
}
</script>

<style scoped>
.vehicle-driver-assignment-page {
  width: 100%;
  min-height: 100vh;
  background-color: #f7f8fa;
  padding: 16px;
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 20px;
}

.section-title .van-icon {
  margin-right: 8px;
}

.tab-container {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;
}

.vacation-tab-container {
  background-color: transparent;
  margin-bottom: 16px;
}

/* 星期视图样式 */
.week-view-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.week-selector {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
}

.week-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.current-week-text {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.week-calendar {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
}

.week-day {
  background-color: #f7f8fa;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  cursor: pointer;
  position: relative;
}

.week-day:hover {
  background-color: #e8f4ff;
}

.week-day.selected {
  background-color: #e8f4ff;
  border: 2px solid #1989fa;
}

.week-day.today {
  background-color: #fff2e8;
}

.day-header {
  margin-bottom: 8px;
}

.day-name {
  font-size: 14px;
  color: #646566;
  margin-bottom: 4px;
}

.day-number {
  font-size: 24px;
  font-weight: 600;
  color: #323233;
}

.today-tag {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: #ff6b3b;
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 4px;
}

.holiday-tag {
  background-color: #fff2e8;
  color: #ff6b3b;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  margin-bottom: 8px;
  display: inline-block;
}

.day-status {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.status-dot.assigned {
  background-color: #07c160;
}

.status-dot.unassigned {
  background-color: #ff6b3b;
}

.date-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16px;
}

.assignment-content {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.loading-container span {
  margin-top: 12px;
  color: #646566;
}

.schedule-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.schedule-item {
  background-color: #f7f8fa;
  border-radius: 8px;
  padding: 16px;
}

.schedule-header {
  margin-bottom: 16px;
}

.schedule-info {
  margin-bottom: 8px;
}

.schedule-time {
  font-size: 18px;
  font-weight: 600;
  color: #1989fa;
  margin-bottom: 4px;
}

.schedule-route {
  font-size: 14px;
  color: #646566;
}

.schedule-stations {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.assignment-form {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
}

.action-buttons {
  margin-top: 16px;
}

/* 假期管理样式 */
.vacation-management-content {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
}

.vacation-content,
.holiday-content {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
}

.button-group {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.vacation-list,
.holiday-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.vacation-item,
.holiday-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  background-color: #f7f8fa;
  border-radius: 8px;
}

.vacation-info,
.holiday-info {
  flex: 1;
}

.vacation-name,
.holiday-name {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.vacation-period,
.holiday-date {
  font-size: 14px;
  color: #646566;
  margin-bottom: 4px;
}

.vacation-desc {
  font-size: 14px;
  color: #969799;
}

.vacation-actions,
.holiday-actions {
  display: flex;
  gap: 8px;
}

.holiday-type {
  margin-top: 4px;
}

/* 弹窗样式 */
.selector-popup,
.vacation-form-popup,
.holiday-form-popup {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebedf0;
}

.popup-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.selector-list {
  flex: 1;
  overflow-y: auto;
}

.selector-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ebedf0;
}

.selector-item.selected {
  background-color: #e8f4ff;
}

.selector-item-info {
  flex: 1;
}

.selector-item-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.selector-item-desc {
  font-size: 14px;
  color: #646566;
}

.selected-icon {
  color: #1989fa;
  font-size: 20px;
}

.placeholder {
  color: #969799;
}

.form-buttons {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.card {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
}
</style>