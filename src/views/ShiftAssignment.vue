<template>
  <div class="vehicle-driver-assignment-page">
    <div class="page-container">
      <!-- 日期选择 -->
      <div class="date-selector card">
        <van-field
          v-model="selectedDateText"
          name="date"
          label="选择日期"
          placeholder="请选择日期"
          readonly
          is-link
          @click="showDatePicker = true"
        />
      </div>
      
      <!-- 班次安排 -->
      <div class="assignment-content card">
        <div v-if="loading" class="loading-container">
          <van-loading type="spinner" color="#1989fa" />
          <span>加载中...</span>
        </div>
        
        <div v-else-if="dateSchedules.length > 0" class="schedule-list">
          <div 
            v-for="schedule in dateSchedules" 
            :key="schedule.id"
            class="schedule-item"
          >
            <div class="schedule-header">
              <div class="schedule-info">
                <div class="schedule-time">{{ schedule.time }}</div>
                <div class="schedule-route">{{ schedule.route }}</div>
              </div>
              <div class="schedule-stations">
                <van-tag 
                  v-for="station in schedule.stations" 
                  :key="station"
                  size="mini"
                  type="primary"
                  plain
                >
                  {{ station }}
                </van-tag>
              </div>
            </div>
            
            <div class="assignment-form">
              <van-cell-group>
                <van-field
                  v-model="schedule.vehicleNumber"
                  name="vehicleNumber"
                  label="车牌号"
                  placeholder="请输入车牌号"
                />
                <van-field
                  v-model="schedule.driverName"
                  name="driverName"
                  label="驾驶员"
                  placeholder="请选择驾驶员"
                  readonly
                  is-link
                  @click="showDriverSelector = true; selectedScheduleForDriver = schedule"
                />
              </van-cell-group>
              
              <div class="action-buttons">
                <van-button size="small" type="primary" @click="saveAssignment(schedule)">
                  保存安排
                </van-button>
              </div>
            </div>
          </div>
        </div>
        
        <van-empty v-else description="该日期暂无班次" />
      </div>
    </div>
    
    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        v-model="selectedDate"
        title="选择日期"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
    
    <!-- 驾驶员选择器 -->
    <van-popup v-model:show="showDriverSelector" position="bottom" :style="{ height: '70%' }">
      <div class="selector-popup">
        <div class="popup-header">
          <h3>选择驾驶员</h3>
          <van-icon name="cross" @click="showDriverSelector = false" />
        </div>
        
        <div class="selector-list">
          <div 
            v-for="driver in driverUsers"
            :key="driver.id"
            class="selector-item"
            :class="{ 'selected': selectedScheduleForDriver?.driverId === driver.id }"
            @click="selectDriver(driver)"
          >
            <div class="selector-item-info">
              <div class="selector-item-title">{{ driver.name }}</div>
              <div class="selector-item-desc">{{ driver.phone }}</div>
            </div>
            <van-icon name="success" v-if="selectedScheduleForDriver?.driverId === driver.id" class="selected-icon" />
          </div>
          <van-empty 
            v-if="driverUsers.length === 0"
            description="暂无驾驶员数据"
          />
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { showToast } from 'vant'
import dayjs from 'dayjs'
import { useBusStore } from '../stores/bus.js'

const busStore = useBusStore()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const selectedDateText = ref('')
const selectedDate = ref([])
const dateSchedules = ref([])

// 驾驶员数据
const driverUsers = ref([
  { id: 'driver1', name: '驾驶员1', phone: '13800138006', type: 'teacher', department: '车队' },
  { id: 'driver2', name: '驾驶员2', phone: '13800138007', type: 'teacher', department: '车队' },
  { id: 'driver3', name: '驾驶员3', phone: '13800138008', type: 'teacher', department: '车队' }
])

// 弹窗控制
const showDatePicker = ref(false)
const showDriverSelector = ref(false)

// 选择的对象
const selectedScheduleForDriver = ref(null)

// 初始化数据
onMounted(() => {
  // 设置默认日期为今天
  const now = new Date()
  selectedDate.value = [now.getFullYear(), now.getMonth() + 1, now.getDate()]
  selectedDateText.value = dayjs().format('YYYY年MM月DD日')
  
  loadDateSchedules()
})

// 加载指定日期的班次
const loadDateSchedules = async () => {
  if (!selectedDate.value || selectedDate.value.length === 0) {
    return
  }
  
  loading.value = true
  try {
    const dateStr = dayjs(selectedDate.value).format('YYYY-MM-DD')
    // 从store获取班次
    const schedules = busStore.getScheduleByDate(dateStr)
    
    // 为每个班次添加车牌号和驾驶员字段
    dateSchedules.value = schedules.map(schedule => ({
      ...schedule,
      vehicleNumber: '',
      driverId: '',
      driverName: ''
    }))
  } catch (error) {
    showToast('加载班次信息失败')
    console.error('加载班次信息失败:', error)
  } finally {
    loading.value = false
  }
}

// 日期选择确认
const onDateConfirm = () => {
  selectedDateText.value = dayjs(selectedDate.value).format('YYYY年MM月DD日')
  showDatePicker.value = false
  loadDateSchedules()
}

// 选择驾驶员
const selectDriver = (driver) => {
  if (selectedScheduleForDriver.value) {
    selectedScheduleForDriver.value.driverId = driver.id
    selectedScheduleForDriver.value.driverName = driver.name
  }
  showDriverSelector.value = false
}

// 保存班次安排
const saveAssignment = async (schedule) => {
  if (!schedule.vehicleNumber.trim()) {
    showToast('请输入车牌号')
    return
  }
  
  if (!schedule.driverId) {
    showToast('请选择驾驶员')
    return
  }
  
  submitting.value = true
  try {
    // 调用bus store中的方法保存数据
    await busStore.saveScheduleAssignment({
      date: dayjs(selectedDate.value).format('YYYY-MM-DD'),
      scheduleId: schedule.id,
      vehicleNumber: schedule.vehicleNumber,
      driverId: schedule.driverId,
      driverName: schedule.driverName
    })
    
    showToast('班次安排保存成功')
  } catch (error) {
    showToast('班次安排保存失败')
    console.error('保存班次安排失败:', error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.vehicle-driver-assignment-page {
  width: 100%;
  min-height: 100vh;
  background-color: #f7f8fa;
  overflow-x: hidden;
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 20px;
  gap: 8px;
}

.date-selector {
  margin-bottom: 16px;
}

.assignment-content {
  margin-bottom: 16px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.loading-container span {
  margin-top: 12px;
  color: #646566;
}

.schedule-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.schedule-item {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.schedule-header {
  margin-bottom: 16px;
}

.schedule-info {
  margin-bottom: 8px;
}

.schedule-time {
  font-size: 18px;
  font-weight: 600;
  color: #1989fa;
  margin-bottom: 4px;
}

.schedule-route {
  font-size: 14px;
  color: #646566;
}

.schedule-stations {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.assignment-form {
  background-color: #f7f8fa;
  border-radius: 8px;
  padding: 16px;
}

.action-buttons {
  margin-top: 16px;
}

.card {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 弹窗样式 */
.selector-popup {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebedf0;
}

.popup-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.selector-list {
  flex: 1;
  overflow-y: auto;
}

.selector-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ebedf0;
}

.selector-item.selected {
  background-color: #e8f4ff;
}

.selector-item-info {
  flex: 1;
}

.selector-item-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.selector-item-desc {
  font-size: 14px;
  color: #646566;
}

.selected-icon {
  color: #1989fa;
  font-size: 20px;
}
</style>