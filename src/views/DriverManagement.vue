<template>
  <div class="driver-management-page">
    <div class="page-container">


      <!-- 驾驶员列表 -->
      <div class="user-list-card card">
        <div class="section-title">
          <van-icon name="friends-o" />
          驾驶员列表 ({{ filteredUsers.length }})
        </div>

        <!-- 搜索和添加按钮 -->
        <div class="filter-row">
          <van-field
            v-model="searchKeyword"
            placeholder="搜索用户姓名或部门"
            left-icon="search"
            clearable
            @change="onSearch"
          />
          <van-button 
            type="primary" 
            size="small"
            @click="showAddUserDialog = true"
          >
            添加
          </van-button>
        </div>

        <!-- 用户列表 -->
        <div class="user-list" v-if="filteredUsers.length > 0">
          <div 
            v-for="user in filteredUsers" 
            :key="user.id"
            class="user-item"
          >
            <div class="user-avatar">
              <van-icon name="user-o" size="24" />
            </div>
            <div class="user-info">
              <div class="user-name">{{ user.name }}</div>
              <div class="user-details">
                {{ user.type === 'teacher' ? '教师' : '学生' }} | {{ user.department }}
              </div>
              <div class="user-contact">
                <van-icon name="phone-o" size="12" />
                {{ user.phone }}
              </div>
            </div>
            <div class="user-actions">
              <van-button 
                type="danger" 
                size="small"
                @click="removeUser(user)"
              >
                移除
              </van-button>
            </div>
          </div>
        </div>

        <van-empty 
          v-else
          description="暂无用户数据"
        />
      </div>
    </div>

    <!-- 添加用户弹窗 -->
    <van-dialog
      v-model:show="showAddUserDialog"
      title="添加驾驶员"
      :confirm-button-text="'确认添加'"
      :before-close="handleDialogClose"
    >
      <div class="add-user-dialog">
        <van-field
          v-model="selectedUserName"
          placeholder="请选择用户"
          readonly
          @click="showUserSelector = true"
        >
          <template #right-icon>
            <van-icon name="arrow-right" />
          </template>
        </van-field>
        <div v-if="selectedUser" class="selected-user-info">
          <p>姓名：{{ selectedUser.name }}</p>
          <p>部门：{{ selectedUser.department }}</p>
          <p>电话：{{ selectedUser.phone }}</p>
        </div>
      </div>
    </van-dialog>

    <!-- 用户选择器 -->
    <van-popup
      v-model:show="showUserSelector"
      position="bottom"
      :style="{ height: '80%' }"
    >
      <div class="user-detail-popup">
        <div class="popup-header">
          <h3>选择用户</h3>
          <div class="header-actions">
            <van-button 
              size="small"
              @click="showUserSelector = false"
            >
              关闭
            </van-button>
          </div>
        </div>
        
        <van-field
          v-model="selectorSearchKeyword"
          placeholder="搜索用户姓名或部门"
          left-icon="search"
          clearable
          @change="onSelectorSearch"
        />
        
        <div class="selector-list">
          <div 
            v-for="user in availableUsersForSelection"
            :key="user.id"
            class="selector-item"
            @click="selectUser(user)"
          >
            <div class="selector-user-info">
              <div class="selector-user-name">{{ user.name }}</div>
              <div class="selector-user-details">{{ user.department }}</div>
            </div>
            <van-icon name="check" size="16" v-if="selectedUser && selectedUser.id === user.id" />
          </div>
          <van-empty 
            v-if="availableUsersForSelection.length === 0"
            description="暂无可选用户"
          />
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useBusStore } from '../stores/bus'
import { showToast } from 'vant'
import { mockUsers } from '../utils/mockData'

export default {
  name: 'RoleManagement',
  setup() {
    const busStore = useBusStore()

    // 状态
    const searchKeyword = ref('')
    const showAddUserDialog = ref(false)
    const showUserSelector = ref(false)
    const selectedUser = ref(null)
    const selectedUserName = ref('')
    const selectorSearchKeyword = ref('')

    // 模拟驾驶员用户数据
    const driverUsers = ref([
      { id: 'driver1', name: '驾驶员1', phone: '13800138006', type: 'teacher', department: '车队' },
      { id: 'driver2', name: '驾驶员2', phone: '13800138007', type: 'teacher', department: '车队' },
      { id: 'driver3', name: '驾驶员3', phone: '13800138008', type: 'teacher', department: '车队' }
    ])

    // 从mockData中获取更多可选用户（排除已在驾驶员中的用户）
    const availableUsers = computed(() => {
      const existingUserIds = new Set([
        ...driverUsers.value.map(u => u.id),
        ...mockUsers.map(u => u.id)
      ])
      
      // 模拟从学校统一用户中心获取用户
      const allUsers = [
        ...mockUsers,
        { id: '4', name: '赵六', phone: '13800138009', type: 'teacher', department: '人事处' },
        { id: '5', name: '孙七', phone: '13800138010', type: 'teacher', department: '财务处' },
        { id: '6', name: '周八', phone: '13800138011', type: 'teacher', department: '保卫处' },
        { id: '7', name: '吴九', phone: '13800138012', type: 'teacher', department: '教务处' },
        { id: '8', name: '郑十', phone: '13800138013', type: 'teacher', department: '科研处' }
      ]
      
      return allUsers.filter(user => !existingUserIds.has(user.id))
    })

    // 过滤后的可选用户（用于选择器）
    const availableUsersForSelection = computed(() => {
      if (!selectorSearchKeyword.value) {
        return availableUsers.value
      }
      const keyword = selectorSearchKeyword.value.toLowerCase()
      return availableUsers.value.filter(user => 
        user.name.toLowerCase().includes(keyword) || 
        user.department.toLowerCase().includes(keyword)
      )
    })

    // 搜索过滤后的驾驶员列表
    const filteredUsers = computed(() => {
      if (!searchKeyword.value) {
        return driverUsers.value
      }
      const keyword = searchKeyword.value.toLowerCase()
      return driverUsers.value.filter(user => 
        user.name.toLowerCase().includes(keyword) || 
        user.department.toLowerCase().includes(keyword)
      )
    })

    // 方法
    const onSearch = () => {
      // 搜索逻辑已在computed中处理
    }

    const selectUser = (user) => {
      selectedUser.value = user
      selectedUserName.value = user.name
      showUserSelector.value = false
    }

    const onSelectorSearch = () => {
      // 搜索逻辑已在computed中处理
    }

    const handleDialogClose = (action) => {
      if (action === 'confirm') {
        if (!selectedUser.value) {
          showToast('请选择用户')
          return false
        }
        
        // 检查用户是否已在驾驶员中
        const isUserExists = driverUsers.value.some(u => u.id === selectedUser.value.id)
        if (isUserExists) {
          showToast('该用户已在驾驶员中')
          return false
        }
        
        // 添加用户到驾驶员
        driverUsers.value.push({
          ...selectedUser.value,
          role: 'driver'
        })
        
        showToast('成功添加为驾驶员')
        
        // 重置选中状态
        selectedUser.value = null
        selectedUserName.value = ''
      }
      return true
    }

    const removeUser = (user) => {
      const index = driverUsers.value.findIndex(u => u.id === user.id)
      if (index > -1) {
        driverUsers.value.splice(index, 1)
        showToast('已移除')
      }
    }

    // 生命周期
    onMounted(() => {
      // 可以在这里初始化数据
    })

    return {
      searchKeyword,
      showAddUserDialog,
      showUserSelector,
      selectedUser,
      selectedUserName,
      selectorSearchKeyword,
      filteredUsers,
      availableUsersForSelection,
      onSearch,
      selectUser,
      onSelectorSearch,
      handleDialogClose,
      removeUser
    }
  }
}
</script>

<style scoped>
.driver-management-page {
  width: 100%;
  min-height: 100%;
  background: #f7f8fa;
  overflow-x: hidden;
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
}

.card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 12px;
}

.filter-row .van-field {
  flex: 1;
}

.user-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-item:active {
  background: #f7f8fa;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: #969799;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.user-details {
  font-size: 12px;
  color: #646566;
  margin-bottom: 4px;
}

.user-contact {
  font-size: 12px;
  color: #969799;
  display: flex;
  align-items: center;
  gap: 4px;
}

.user-actions {
  margin-left: 8px;
}

/* 添加用户弹窗样式 */
.add-user-dialog {
  padding: 8px 0;
}

.selected-user-info {
  margin-top: 12px;
  padding: 12px;
  background: #f7f8fa;
  border-radius: 8px;
}

.selected-user-info p {
  margin: 4px 0;
  font-size: 14px;
  color: #646566;
}

/* 用户选择器样式 */
.user-detail-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebedf0;
}

.popup-header h3 {
  margin: 0;
  font-size: 16px;
  color: #323233;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.selector-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.selector-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f7f8fa;
  border-radius: 8px;
}

.selector-item:active {
  background: #e8e8e8;
}

.selector-user-info {
  flex: 1;
}

.selector-user-name {
  font-size: 14px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.selector-user-details {
  font-size: 12px;
  color: #646566;
}
</style>