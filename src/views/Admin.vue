<template>
  <div class="admin-page">
    <div class="page-container">
      <!-- 统计概览 -->
      <div class="stats-overview card">
        <div class="section-title">
          <van-icon name="bar-chart-o" />
          今日统计
        </div>
        <van-grid :column-num="2" :border="false">
          <van-grid-item>
            <div class="stat-item">
              <div class="stat-number">{{ todayStats.totalReservations }}</div>
              <div class="stat-label">总预约</div>
            </div>
          </van-grid-item>
          <van-grid-item>
            <div class="stat-item">
              <div class="stat-number">{{ todayStats.checkedIn }}</div>
              <div class="stat-label">已签到</div>
            </div>
          </van-grid-item>
          <van-grid-item>
            <div class="stat-item">
              <div class="stat-number">{{ todayStats.noShow }}</div>
              <div class="stat-label">爽约</div>
            </div>
          </van-grid-item>
          <van-grid-item>
            <div class="stat-item">
              <div class="stat-number">{{ todayStats.activeBuses }}</div>
              <div class="stat-label">运行班次</div>
            </div>
          </van-grid-item>
        </van-grid>
      </div>

      <!-- 班车管理 -->
      <div class="bus-management card">
        <div class="section-title">
          <van-icon name="logistics" />
          班车管理
        </div>
        
        <!-- 日期选择 -->
        <div class="date-selector">
          <van-field
            v-model="selectedDateText"
            name="date"
            label="查看日期"
            placeholder="选择日期"
            readonly
            is-link
            @click="showDatePicker = true"
          />
        </div>

        <!-- 班车列表 -->
        <div class="bus-schedule-list" v-if="selectedDateSchedules.length > 0">
          <div 
            v-for="schedule in selectedDateSchedules" 
            :key="schedule.id"
            class="schedule-item"
          >
            <div class="schedule-header">
              <div class="schedule-info">
                <div class="schedule-time">{{ schedule.time }}</div>
                <div class="schedule-route">{{ schedule.route }}</div>
              </div>
              <van-button 
                size="small" 
                type="primary" 
                @click="viewPassengers(schedule)"
              >
                查看乘客 ({{ getPassengerCount(schedule.id) }})
              </van-button>
            </div>
            
            <div class="schedule-stations">
              <van-tag 
                v-for="station in schedule.stations" 
                :key="station"
                size="mini"
                type="primary"
                plain
              >
                {{ station }}
              </van-tag>
            </div>
          </div>
        </div>
      </div>



      <!-- 功能菜单 -->
      <div class="admin-functions card">
        <div class="section-title">
          <van-icon name="setting-o" />
          管理功能
        </div>
        <van-cell-group>
          <van-cell
            title="导出乘客名单"
            is-link
            @click="exportPassengerList"
          >
            <template #icon>
              <van-icon name="down" />
            </template>
          </van-cell>
          <van-cell
            title="统计报表"
            is-link
            @click="showStatsReport"
          >
            <template #icon>
              <van-icon name="bar-chart-o" />
            </template>
          </van-cell>
          <van-cell
            title="爽约用户管理"
            is-link
            @click="goToNoShowUserManagement"
          >
            <template #icon>
              <van-icon name="warning-o" />
            </template>
            <template #value>
              <van-tag type="danger" size="mini" v-if="noShowStats.totalUsers > 0">
                {{ noShowStats.totalUsers }}
              </van-tag>
            </template>
          </van-cell>
          <van-cell
            title="驾驶员管理"
            is-link
            @click="goToRoleManagement"
          >
            <template #icon>
              <van-icon name="user-circle-o" />
            </template>
          </van-cell>
          <van-cell
            title="假期日期管理"
            is-link
            @click="goToVacationManagement"
          >
            <template #icon>
              <van-icon name="calendar" />
            </template>
          </van-cell>
          <van-cell
            title="班次安排"
            is-link
            @click="goToVehicleDriverAssignment"
          >
            <template #icon>
              <van-icon name="truck" />
            </template>
          </van-cell>
          <van-cell
            title="推送通知"
            is-link
            @click="showNotificationPanel"
          >
            <template #icon>
              <van-icon name="chat-o" />
            </template>
          </van-cell>
        </van-cell-group>
      </div>
    </div>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        v-model="selectedDate"
        title="选择日期"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>

    <!-- 乘客列表弹窗 -->
    <van-popup 
      v-model:show="showPassengerList" 
      position="bottom" 
      :style="{ height: '70%' }"
    >
      <div class="passenger-popup">
        <div class="popup-header">
          <h3>{{ selectedSchedule?.time }} {{ selectedSchedule?.route }}</h3>
          <van-button 
            type="primary" 
            size="small"
            @click="exportCurrentList"
          >
            导出
          </van-button>
        </div>
        
        <div class="passenger-list">
          <div 
            v-for="passenger in currentPassengers" 
            :key="passenger.id"
            class="passenger-item"
          >
            <div class="passenger-info">
              <div class="passenger-name">{{ passenger.userName }}</div>
              <div class="passenger-details">
                {{ passenger.userType === 'teacher' ? '教师' : '学生' }} | 
                {{ passenger.userDepartment }}
              </div>
              <div class="passenger-station">
                <van-icon name="location-o" />
                {{ passenger.station }}
              </div>
            </div>
            <div class="passenger-status">
              <van-tag 
                :type="passenger.status === 'completed' ? 'success' : 'warning'"
              >
                {{ passenger.status === 'completed' ? '已签到' : '未签到' }}
              </van-tag>
              <div class="passenger-phone">{{ passenger.phone }}</div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 用户详情弹窗 -->
    <van-popup
      v-model:show="showUserDetail"
      position="bottom"
      :style="{ height: '70%' }"
    >
      <div class="user-detail-popup" v-if="selectedUser">
        <div class="popup-header">
          <h3>{{ selectedUser.userName }} - 爽约详情</h3>
          <van-button
            type="danger"
            size="small"
            @click="toggleUserRestriction"
            :disabled="processingRestriction"
          >
            {{ selectedUser.isRestricted ? '解除限制' : '限制预约' }}
          </van-button>
        </div>

        <!-- 用户基本信息 -->
        <div class="user-basic-info">
          <div class="info-row">
            <span class="label">姓名：</span>
            <span class="value">{{ selectedUser.userName }}</span>
          </div>
          <div class="info-row">
            <span class="label">身份：</span>
            <span class="value">{{ selectedUser.userType === 'teacher' ? '教师' : '学生' }}</span>
          </div>
          <div class="info-row">
            <span class="label">部门：</span>
            <span class="value">{{ selectedUser.department }}</span>
          </div>
          <div class="info-row">
            <span class="label">电话：</span>
            <span class="value">{{ selectedUser.phone }}</span>
          </div>
          <div class="info-row">
            <span class="label">爽约次数：</span>
            <span class="value danger">{{ selectedUser.noShowCount }} 次</span>
          </div>
          <div class="info-row">
            <span class="label">状态：</span>
            <span class="value" :class="{ 'danger': selectedUser.isRestricted, 'success': !selectedUser.isRestricted }">
              {{ selectedUser.isRestricted ? '已限制预约' : '正常' }}
            </span>
          </div>
        </div>

        <!-- 爽约记录 -->
        <div class="user-noshow-records">
          <h4>爽约记录</h4>
          <div class="records-list">
            <div
              v-for="record in selectedUser.records"
              :key="record.id"
              class="record-item"
            >
              <div class="record-info">
                <div class="record-date">{{ formatDate(record.date) }}</div>
                <div class="record-bus">{{ getBusInfo(record.busId) }}</div>
                <div class="record-time">{{ formatDateTime(record.recordTime) }}</div>
              </div>
              <van-button
                size="mini"
                type="danger"
                @click="removeNoShowRecord(record.id)"
              >
                删除
              </van-button>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useBusStore } from '../stores/bus.js'
import { showToast } from 'vant'
import dayjs from 'dayjs'
import { initMockData } from '../utils/mockData.js'

const router = useRouter()
const busStore = useBusStore()

// 响应式数据
const showDatePicker = ref(false)
const showPassengerList = ref(false)
const showUserDetail = ref(false)
// Vant 4的date-picker组件要求v-model绑定值为数组格式 [年, 月, 日]
const now = new Date();
const selectedDate = ref([now.getFullYear(), now.getMonth() + 1, now.getDate()])
const selectedDateText = ref('')
const selectedSchedule = ref(null)
const selectedUser = ref(null)
const processingRestriction = ref(false)

// 计算属性
const myReservations = computed(() => busStore.myReservations)

const selectedDateSchedules = computed(() => {
  const dateStr = dayjs(selectedDate.value).format('YYYY-MM-DD')
  return busStore.getScheduleByDate(dateStr)
})

const todayStats = computed(() => {
  const today = dayjs().format('YYYY-MM-DD')
  const todayReservations = myReservations.value.filter(r => r.date === today)
  
  return {
    totalReservations: todayReservations.length,
    checkedIn: todayReservations.filter(r => r.status === 'completed').length,
    noShow: todayReservations.filter(r => r.status === 'confirmed' && dayjs().isAfter(dayjs(`${r.date} ${r.busTime}`).add(30, 'minute'))).length,
    activeBuses: selectedDateSchedules.value.length
  }
})

const currentPassengers = computed(() => {
  if (!selectedSchedule.value) return []

  const dateStr = dayjs(selectedDate.value).format('YYYY-MM-DD')
  return myReservations.value.filter(r =>
    r.date === dateStr &&
    r.busId === selectedSchedule.value.id &&
    r.status !== 'cancelled'
  )
})

// 爽约管理相关计算属性
const noShowUsers = computed(() => {
  // 模拟爽约用户数据，实际应该从后端获取
  const users = new Map()

  // 统计每个用户的爽约记录
  busStore.noShowRecords.forEach(record => {
    const reservation = myReservations.value.find(r => r.id === record.reservationId)
    if (reservation) {
      const userId = reservation.userName
      if (!users.has(userId)) {
        users.set(userId, {
          userId,
          userName: reservation.userName,
          userType: reservation.userType,
          department: reservation.userDepartment,
          phone: reservation.phone,
          records: [],
          noShowCount: 0,
          isRestricted: false
        })
      }

      const user = users.get(userId)
      user.records.push(record)
      user.noShowCount = user.records.length
      user.isRestricted = user.noShowCount >= 3
    }
  })

  return Array.from(users.values()).filter(user => user.noShowCount > 0)
})

const noShowStats = computed(() => {
  const users = noShowUsers.value
  return {
    totalUsers: users.length,
    restrictedUsers: users.filter(u => u.isRestricted).length,
    totalRecords: users.reduce((sum, u) => sum + u.noShowCount, 0)
  }
})

// 方法
const getPassengerCount = (busId) => {
  const dateStr = dayjs(selectedDate.value).format('YYYY-MM-DD')
  return myReservations.value.filter(r => 
    r.date === dateStr && 
    r.busId === busId &&
    r.status !== 'cancelled'
  ).length
}

const onDateConfirm = () => {
  selectedDateText.value = dayjs(selectedDate.value).format('YYYY年MM月DD日')
  showDatePicker.value = false
}

const viewPassengers = (schedule) => {
  selectedSchedule.value = schedule
  showPassengerList.value = true
}

const exportPassengerList = () => {
  // 导出所有班次的乘客名单
  showToast('导出功能开发中...')
}

const exportCurrentList = () => {
  // 导出当前班次的乘客名单
  if (currentPassengers.value.length === 0) {
    showToast('当前班次无乘客')
    return
  }
  
  // 这里应该生成Excel或PDF文件
  showToast('导出功能开发中...')
}

const showStatsReport = () => {
  showToast('统计报表功能开发中...')
}

const showNotificationPanel = () => {
  showToast('推送通知功能开发中...')
}

const goToNoShowUserManagement = () => {
  router.push('/noshow-user-management')
}

const goToRoleManagement = () => {
  router.push('/driver-management')
}

const goToVacationManagement = () => {
  router.push('/vacation-management')
}

const goToVehicleDriverAssignment = () => {
  router.push('/vehicle-driver-assignment')
}

// 爽约管理相关方法
const refreshNoShowData = () => {
  showToast('数据已刷新')
  // 这里应该调用后端API刷新数据
}

const viewUserDetail = (user) => {
  selectedUser.value = user
  showUserDetail.value = true
}

const toggleUserRestriction = async () => {
  if (!selectedUser.value) return

  processingRestriction.value = true

  try {
    // 这里应该调用后端API
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用

    selectedUser.value.isRestricted = !selectedUser.value.isRestricted
    showToast(selectedUser.value.isRestricted ? '已限制该用户预约' : '已解除该用户限制')
  } catch (error) {
    showToast('操作失败，请重试')
  } finally {
    processingRestriction.value = false
  }
}

const removeNoShowRecord = async (recordId) => {
  try {
    // 这里应该调用后端API删除记录
    const recordIndex = selectedUser.value.records.findIndex(r => r.id === recordId)
    if (recordIndex > -1) {
      selectedUser.value.records.splice(recordIndex, 1)
      selectedUser.value.noShowCount = selectedUser.value.records.length
      selectedUser.value.isRestricted = selectedUser.value.noShowCount >= 3

      // 同时从store中删除
      const storeIndex = busStore.noShowRecords.findIndex(r => r.id === recordId)
      if (storeIndex > -1) {
        busStore.noShowRecords.splice(storeIndex, 1)
      }

      showToast('爽约记录已删除')
    }
  } catch (error) {
    showToast('删除失败，请重试')
  }
}

const formatDate = (date) => {
  return dayjs(date).format('YYYY年MM月DD日')
}

const formatDateTime = (datetime) => {
  return dayjs(datetime).format('MM月DD日 HH:mm')
}

const getBusInfo = (busId) => {
  const allSchedules = [
    ...busStore.busSchedules.weekday,
    ...busStore.busSchedules.weekend,
    ...busStore.busSchedules.vacation
  ]

  const bus = allSchedules.find(s => s.id === busId)
  return bus ? `${bus.time} ${bus.route}` : '未知班车'
}

// 生命周期
onMounted(() => {
  selectedDateText.value = dayjs().format('YYYY年MM月DD日')
})
</script>

<style scoped>
.admin-page {
  width: 100%;
  min-height: 100%;
  background: #f7f8fa;
  overflow-x: hidden;
}

.stat-item {
  text-align: center;
  padding: 12px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #1989fa;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #646566;
}

.date-selector {
  margin-bottom: 16px;
}

.bus-schedule-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.schedule-item {
  padding: 12px;
  background: #f7f8fa;
  border-radius: 8px;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.schedule-info {
  flex: 1;
}

.schedule-time {
  font-size: 18px;
  font-weight: 600;
  color: #1989fa;
  margin-bottom: 4px;
}

.schedule-route {
  font-size: 14px;
  color: #646566;
}

.schedule-stations {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.passenger-popup {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebedf0;
}

.popup-header h3 {
  margin: 0;
  font-size: 16px;
  color: #323233;
}

.passenger-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.passenger-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.passenger-info {
  flex: 1;
}

.passenger-name {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.passenger-details {
  font-size: 12px;
  color: #646566;
  margin-bottom: 4px;
}

.passenger-station {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #969799;
}

.passenger-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.passenger-phone {
  font-size: 12px;
  color: #969799;
}

/* 管理功能图标对齐样式 */
.van-cell {
  align-items: center !important;
}

.van-cell__left-icon {
  margin-right: 10px;
}

/* 爽约管理样式 */
.noshow-stats {
  margin-bottom: 16px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.user-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.user-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-item:active {
  background: #f7f8fa;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.user-details {
  font-size: 12px;
  color: #646566;
  margin-bottom: 4px;
}

.user-phone {
  font-size: 12px;
  color: #969799;
}

.user-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.status-text {
  font-size: 12px;
  color: #969799;
}

/* 用户详情弹窗样式 */
.user-detail-popup {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebedf0;
}

.popup-header h3 {
  margin: 0;
  font-size: 16px;
  color: #323233;
}

.user-basic-info {
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f7f8fa;
}

.info-row .label {
  font-size: 14px;
  color: #646566;
}

.info-row .value {
  font-size: 14px;
  color: #323233;
  font-weight: 500;
}

.info-row .value.success {
  color: #07c160;
}

.info-row .value.danger {
  color: #ee0a24;
}

.user-noshow-records {
  flex: 1;
  overflow-y: auto;
}

.user-noshow-records h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #323233;
}

.user-noshow-records .records-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-noshow-records .record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: #f7f8fa;
  border-radius: 6px;
}

.user-noshow-records .record-info {
  flex: 1;
}

.user-noshow-records .record-date {
  font-size: 14px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 2px;
}

.user-noshow-records .record-bus {
  font-size: 12px;
  color: #646566;
  margin-bottom: 2px;
}

.user-noshow-records .record-time {
  font-size: 12px;
  color: #969799;
}
</style>
