<template>
  <div class="home-page">
    <div class="page-container">
    <!-- 用户信息卡片 -->
    <div class="user-card card">
      <div class="user-info">
        <van-icon name="user-o" size="24" />
        <div class="user-details">
          <div class="user-name">{{ userInfo.user?.nickName }}</div>
          <!-- 爽约状态提示 -->
          <!-- <div class="user-noshow-status">
            <van-tag
              :type="noShowStatusType"
              size="mini"
              @click="goToNoShowManagement"
              class="clickable-tag"
            >
              {{ noShowStatusText }}
            </van-tag>
          </div> -->
        </div>
      </div>
      <!-- <div class="user-actions">
        <van-button
          type="default"
          size="mini"
          @click="goToNoShowManagement"
          icon="warning-o"
        >
          爽约记录
        </van-button>
      </div> -->
    </div>

    <!-- 快速预约 -->
    <div class="quick-reserve card">
      <div class="section-title">
        <van-icon name="calendar-o" />
        快速预约
      </div>
      <div class="date-selector">
        <van-button 
          v-for="date in availableDates" 
          :key="date.date"
          :type="selectedDate === date.date ? 'primary' : 'default'"
          size="small"
          @click="selectedDate = date.date; loadSchedulesByDate(date.date)"
          class="date-btn"
        >
          {{ date.dateText }} {{ date.dayText }}
        </van-button>
      </div>
      
      <!-- 班车列表 -->
      <div class="bus-list" v-if="selectedDateSchedules.length > 0">
        <div 
          v-for="schedule in selectedDateSchedules" 
          :key="schedule.id"
          class="bus-item"
          @click="goToReservation(schedule)"
        >
          <div class="bus-content">
            <div class="bus-info">
              <div class="bus-time">{{ schedule.busTime }}</div>
              <div class="bus-route">{{ schedule.start_station }} → {{ schedule.end_station }}</div>
            </div>
            <div class="bus-stations">
              <template v-for="(station, index) in schedule.station.split(',')" :key="index">
                <van-tag 
                  size="mini"
                  type="primary"
                  plain
                >
                  {{ station }}
                </van-tag>
              </template>

            </div>
          </div>
          <van-icon name="arrow" />
        </div>
      </div>
      
      <van-empty 
        v-else-if="!loading"
        description="今日无班车安排"
      />
      
      <!-- 加载状态 -->
      <van-loading v-if="loading" type="spinner" class="loading-container" />
    </div>

    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useBusStore } from '../stores/bus.js'
import { showToast } from 'vant'
import dayjs from 'dayjs'
import { schedulesApi } from '../api/schedules.js'

const router = useRouter()
const busStore = useBusStore()

// 响应式数据
const selectedDate = ref('')
const availableDates = ref([]) 
const selectedDateSchedules = ref([]) 
const loading = ref(false)

// 计算属性
const userInfo = computed(() => busStore.userInfo)


const noShowStatusText = computed(() => {
  const count = busStore.noShowRecords.num
  if (count === 0) return '无爽约记录'
  if (count < 3) return `${count}次爽约`
  return '已限制预约'
})

const noShowStatusType = computed(() => {
  const count = busStore.noShowRecords.num
  if (count === 0) return 'success'
  if (count < 3) return 'warning'
  return 'danger'
})

// 方法
const goToReservation = (schedule) => {
  // 将完整的班次数据作为路由参数传递
  router.push({
    name: 'Reservation',
    query: {
      schedule: JSON.stringify(schedule)
    }
  })
}

const goToNoShowManagement = () => {
  router.push('/noshow-management')
}

// 生成可预约日期范围
const generateAvailableDates = () => {
  const dates = []
  const now = dayjs()
  const cutoffTime = now.hour(18).minute(0).second(0)
  
  // 确定起始日期
  let startDate
  if (now.isAfter(cutoffTime)) {
    startDate = now.add(2, 'day') // 后天开始
  } else {
    startDate = now.add(1, 'day') // 明天开始
  }
  
  // 生成未来7天的可预约日期
  for (let i = 0; i < 2; i++) {
    const date = startDate.add(i, 'day')
    dates.push({
      date: date.format('YYYY-MM-DD'),
      dateText: date.format('MM月DD日'),
      dayText: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.day()]
    })
  }
  
  availableDates.value = dates
}

// 根据日期加载班次
const loadSchedulesByDate = async (date) => {
  if (!date) return
  
  loading.value = true
  try {
    const response = await schedulesApi.list({ busDate:date })
    // 确保response是数组
    const schedules = Array.isArray(response) ? response : []
    
    // 根据接口文档格式调整数据结构
    selectedDateSchedules.value = schedules
   
  } catch (error) {
    showToast('获取班次信息失败，请稍后重试')
    console.error('获取班次信息失败:', error)
    selectedDateSchedules.value = []
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  // 生成可预约日期
  generateAvailableDates()
  // 加载爽约记录
  busStore.recordNoShow()

  // 设置默认选中日期为第一个可预约日期
  if (availableDates.value.length > 0) {
    selectedDate.value = availableDates.value[0].date
    // 加载默认日期的班次
    loadSchedulesByDate(selectedDate.value)
  }
})
</script>

<style scoped>
.home-page {
  width: 100%;
  min-height: 100%;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  overflow-x: hidden;
}

.user-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px 10px;
}

.user-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-end;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.user-noshow-status {
  margin-top: 4px;
}

.clickable-tag {
  cursor: pointer;
  transition: opacity 0.2s;
}

.clickable-tag:active {
  opacity: 0.7;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16px;
}

.date-selector {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.date-btn {
  flex: 1;
}

.bus-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.bus-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f7f8fa;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.bus-item:active {
  background: #ebedf0;
}

.bus-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.bus-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.bus-time {
  font-size: 18px;
  font-weight: 600;
  color: #1989fa;
}

.bus-route {
  font-size: 14px;
  color: #646566;
}

.bus-stations {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.station-separator {
  color: #909399;
  font-size: 12px;
}

.loading-container {
  padding: 20px 0;
  text-align: center;
}
</style>
