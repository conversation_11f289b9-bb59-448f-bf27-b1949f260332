<template>
  <div class="checkin-page">
    <div class="page-container">
      <!-- 当前时间显示 -->
      <div class="time-card card">
        <div class="current-time">{{ currentTime }}</div>
        <div class="current-date">{{ currentDate }}</div>
      </div>

      <!-- 今日班车列表 -->
      <div class="today-buses card">
        <div class="section-title">
          <van-icon name="calendar-o" /> 今日班车
        </div>
        
        <div class="bus-list" v-if="todayReservations.length > 0">
          <div 
            v-for="reservation in todayReservations" 
            :key="reservation.id"
            class="bus-item"
            :class="{ 'completed': reservation.state === 'completed' }"
          >
            <div class="bus-info">
              <div class="bus-time">{{ getBusTime(reservation) }}</div>
              <div class="bus-route">{{ getBusRoute(reservation) }}</div>
              <div class="bus-station">
                <van-icon name="location-o" /> {{ reservation.departureSpot }}
              </div>
            </div>
            
            <div class="checkin-status">
              <van-button 
                v-if="reservation.state === 'confirmed'"
                type="primary"
                size="small"
                @click="checkIn(reservation)"
                :disabled="!canCheckIn(reservation)"
              >
                {{ getCheckInButtonText(reservation) }}
              </van-button>
              
              <div v-else-if="reservation.state === 'completed'" class="completed-status">
                <van-icon name="checked" color="#07c160" />
                <span>已签到</span>
                <div class="checkin-time">{{ formatTime(reservation.signTime) }}</div>
              </div>
              
              <van-tag v-else :type="getStatusTagType(reservation.state)">
                {{ getStatusText(reservation.state) }}
              </van-tag>
            </div>
          </div>
        </div>

        <van-empty 
          v-else
          description="今日无预约班车"
          image="https://fastly.jsdelivr.net/npm/@vant/assets/custom-empty-image.png"
        >
          <van-button 
            type="primary" 
            size="small"
            @click="$router.push('/')"
          >
            立即预约
          </van-button>
        </van-empty>
      </div>

      <!-- 签到记录 -->
      <div class="checkin-history card">
        <div class="section-title">
          <van-icon name="clock-o" /> 最近签到记录
        </div>
        
        <div class="history-list" v-if="recentCheckIns.length > 0">
          <div 
            v-for="record in recentCheckIns" 
            :key="record.id"
            class="history-item"
          >
            <div class="record-info">
              <div class="record-date">{{ formatDate(record.date) }}</div>
              <div class="record-bus">{{ getBusTime(record) }} {{ getBusRoute(record) }}</div>
              <div class="record-station">{{ record.departureSpot }}</div>
            </div>
            <div class="record-time">
              <van-icon name="checked" color="#07c160" />
              {{ formatTime(record.signTime) }}
            </div>
          </div>
        </div>

        <van-empty 
          v-else
          description="暂无签到记录"
          image="https://fastly.jsdelivr.net/npm/@vant/assets/custom-empty-image.png"
        />
      </div>

      <!-- 扫码签到 -->
      <div class="qr-checkin card">
        <div class="section-title">
          <van-icon name="scan" /> 扫码签到
        </div>
        <div class="qr-content">
          <van-button 
            type="primary" 
            block
            icon="scan"
            @click="startQRScan"
          >
            扫描二维码签到
          </van-button>
          <p class="qr-tip">请扫描车上的二维码进行签到确认</p>
        </div>
      </div>
    </div>

    <!-- 签到确认弹窗 -->
    <van-dialog
      v-model:show="showCheckInDialog"
      title="确认签到"
      :message="checkInMessage"
      show-cancel-button
      @confirm="confirmCheckIn"
    />

    <!-- 签到成功弹窗 -->
    <van-dialog
      v-model:show="showSuccessDialog"
      title="签到成功"
      :message="successMessage"
      confirm-button-text="确定"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useBusStore } from '../stores/bus.js'
import { showToast, showConfirmDialog } from 'vant'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import dingTalk from '../utils/dingtalk.js'

dayjs.locale('zh-cn')

const route = useRoute()
const router = useRouter()
const busStore = useBusStore()

// 响应式数据
const currentTime = ref('')
const currentDate = ref('')
const showCheckInDialog = ref(false)
const showSuccessDialog = ref(false)
const selectedReservation = ref(null)
const checkInMessage = ref('')
const successMessage = ref('')
let timeInterval = null

// 计算属性
const myReservations = computed(() => busStore.myReservations)

const todayReservations = computed(() => {
  const today = dayjs().format('YYYY-MM-DD')
  return myReservations.value
    .filter(r => r.date === today)
    .sort((a, b) => a.busSchedules?.time.localeCompare(b.busSchedules?.time) || 0)
})

const recentCheckIns = computed(() => {
  const today = dayjs().format('YYYY-MM-DD')
  return myReservations.value
    .filter(r => r.state === 'completed' && r.signTime && r.date !== today)
    .sort((a, b) => dayjs(b.signTime).valueOf() - dayjs(a.signTime).valueOf())
    .slice(0, 5)
})

// 方法
const updateTime = () => {
  const now = dayjs()
  currentTime.value = now.format('HH:mm:ss')
  currentDate.value = now.format('YYYY年MM月DD日 dddd')
}

const formatTime = (time) => {
  if (!time) return '--:--'
  return dayjs(time).format('HH:mm')
}

const formatDate = (date) => {
  return dayjs(date).format('MM月DD日')
}

const getStatusText = (state) => {
  const statusMap = {
    pending: '待确认',
    confirmed: '已确认',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[state] || '未知'
}

const getStatusTagType = (state) => {
  const typeMap = {
    pending: 'warning',
    confirmed: 'primary',
    completed: 'success',
    cancelled: 'default'
  }
  return typeMap[state] || 'default'
}

const canCheckIn = (reservation) => {
  const now = dayjs()
  const busTime = dayjs(`${reservation.date} ${reservation.busSchedules?.time}`)
  
  // 只能在班车发车前30分钟到发车后30分钟内签到
  const checkInStart = busTime.subtract(30, 'minute')
  const checkInEnd = busTime.add(30, 'minute')
  
  return now.isAfter(checkInStart) && now.isBefore(checkInEnd)
}

const getCheckInButtonText = (reservation) => {
  if (!canCheckIn(reservation)) {
    const now = dayjs()
    const busTime = dayjs(`${reservation.date} ${reservation.busSchedules?.time}`)
    
    if (now.isBefore(busTime.subtract(30, 'minute'))) {
      return '未到签到时间'
    } else {
      return '签到时间已过'
    }
  }
  return '立即签到'
}

const getBusTime = (reservation) => {
  return reservation.busSchedules?.time || ''
}

const getBusRoute = (reservation) => {
  if (reservation.busSchedules) {
    return `${reservation.busSchedules.start_station} - ${reservation.busSchedules.end_station}`
  }
  return ''
}

const checkIn = async (reservation) => {
  if (!canCheckIn(reservation)) {
    showToast('不在签到时间范围内')
    return
  }

  await performCheckIn(reservation)
}

const confirmCheckIn = () => {
  if (selectedReservation.value) {
    busStore.checkIn(selectedReservation.value.id)

    successMessage.value = `签到成功！\n班车：${getBusTime(selectedReservation.value)} ${getBusRoute(selectedReservation.value)}\n签到时间：${dayjs().format('HH:mm:ss')}`
    showSuccessDialog.value = true

    selectedReservation.value = null
  }
  showCheckInDialog.value = false
}

const startQRScan = async () => {
  try {
    // 检查是否在钉钉环境中
    if (!dingTalk.isDingTalkEnv()) {
      // 非钉钉环境，使用模拟数据
      showToast('当前非钉钉环境，使用模拟扫码')
      setTimeout(() => {
        const mockQRData = 'bus_checkin_' + Date.now()
        handleQRResult(mockQRData)
      }, 1000)
      return
    }

    // 钉钉环境，调用真实扫码功能
    showToast('正在启动扫码...')
    const scanResult = await dingTalk.scanQRCode()

    if (scanResult.success) {
      handleQRResult(scanResult.text)
    } else {
      showToast('扫码取消')
    }
  } catch (error) {
    console.error('扫码失败:', error)
    showToast('扫码失败，请重试')
  }
}

const handleQRResult = async (qrData) => {
  try {
    console.log('QR扫描结果:', qrData)

    // 解析二维码数据
    // 预期格式: bus_checkin_[busId]_[date] 或 bus_checkin_[timestamp]
    if (!qrData.startsWith('bus_checkin_')) {
      showToast('无效的签到二维码')
      return
    }

    // 查找今日的预约记录
    const todayReservationsList = todayReservations.value.filter(r => r.state === 'confirmed')

    if (todayReservationsList.length === 0) {
      showToast('今日无可签到的预约记录')
      return
    }

    // 如果只有一个预约，直接签到
    if (todayReservationsList.length === 1) {
      const reservation = todayReservationsList[0]
      if (canCheckIn(reservation)) {
        await performCheckIn(reservation, qrData)
      } else {
        showToast('当前不在签到时间范围内')
      }
      return
    }

    // 多个预约，让用户选择
    showToast('检测到多个预约记录，请手动选择签到')
  } catch (error) {
    console.error('处理二维码失败:', error)
    showToast('签到失败，请重试')
  }
}

const performCheckIn = async (reservation, qrData = '') => {
  try {
    // 使用钉钉确认对话框
    const confirmed = await dingTalk.showConfirm(
      '确认签到',
      `班车：${getBusTime(reservation)} ${getBusRoute(reservation)}\n站点：${reservation.departureSpot}\n${qrData ? '扫码' : '手动'}签到`
    )

    if (confirmed) {
      busStore.checkIn(reservation.id)

      // 使用钉钉成功提示
      await dingTalk.showSuccessToast('签到成功！')

      // 更新成功消息
      successMessage.value = `签到成功！\n班车：${getBusTime(reservation)} ${getBusRoute(reservation)}\n签到时间：${dayjs().format('HH:mm:ss')}`
      showSuccessDialog.value = true
    }
  } catch (error) {
    console.error('签到失败:', error)
    await dingTalk.showErrorToast('签到失败，请重试')
  }
}

// 生命周期
onMounted(async () => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)

  // 初始化钉钉SDK
  try {
    await dingTalk.init()
    if (dingTalk.isDingTalkEnv()) {
      // 设置钉钉导航栏
      await dingTalk.setNavigation({
        title: '签到确认',
        showBack: true
      })
    }
  } catch (error) {
    console.error('钉钉SDK初始化失败:', error)
  }

  // 如果从路由参数传入了预约ID，直接显示签到确认
  const { reservationId } = route.query
  if (reservationId) {
    const reservation = myReservations.value.find(r => r.id === Number(reservationId))
    if (reservation && reservation.state === 'confirmed') {
      checkIn(reservation)
    }
  }
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
.checkin-page {
  width: 100%;
  min-height: 100%;
  background: #f7f8fa;
  overflow-x: hidden;
}

.time-card {
  text-align: center;
  margin-bottom: 16px;
}

.current-time {
  font-size: 32px;
  font-weight: 600;
  color: #1989fa;
  margin-bottom: 8px;
}

.current-date {
  font-size: 14px;
  color: #646566;
}

.bus-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.bus-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f7f8fa;
  border-radius: 8px;
  transition: all 0.2s;
}

.bus-item.completed {
  background: #f0f9ff;
  border: 1px solid #e1f5fe;
}

.bus-info {
  flex: 1;
}

.bus-time {
  font-size: 18px;
  font-weight: 600;
  color: #1989fa;
  margin-bottom: 4px;
}

.bus-route {
  font-size: 14px;
  color: #646566;
  margin-bottom: 4px;
}

.bus-station {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #969799;
}

.checkin-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.completed-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.completed-status span {
  font-size: 12px;
  color: #07c160;
}

.checkin-time {
  font-size: 10px;
  color: #969799;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f7f8fa;
  border-radius: 8px;
}

.record-info {
  flex: 1;
}

.record-date {
  font-size: 14px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.record-bus {
  font-size: 12px;
  color: #646566;
  margin-bottom: 2px;
}

.record-station {
  font-size: 12px;
  color: #969799;
}

.record-time {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #07c160;
}

.qr-content {
  text-align: center;
}

.qr-tip {
  margin-top: 12px;
  font-size: 12px;
  color: #969799;
  line-height: 1.4;
}
</style>
