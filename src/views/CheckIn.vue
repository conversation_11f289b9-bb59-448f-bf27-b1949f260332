<template>
  <div class="checkin-page">
    <div class="page-container">
      <!-- 当前时间显示 -->
      <div class="time-card card">
        <div class="current-time">{{ currentTime }}</div>
        <div class="current-date">{{ currentDate }}</div>
      </div>

      <!-- 今日班车列表 -->
      <div class="today-buses card">
        <div class="section-title">
          <van-icon name="calendar-o" /> 今日班车
        </div>
        
        <div class="bus-list" v-if="todayReservations.length > 0">
          <div 
            v-for="reservation in todayReservations" 
            :key="reservation.id"
            class="bus-item"
            :class="{ 'completed': reservation.state === '1' }"
          >
            <div class="bus-info">
              <div class="bus-time">{{ getBusTime(reservation) }}</div>
              <div class="bus-route">{{ getBusRoute(reservation) }}</div>
              <div class="bus-station">
                <van-icon name="location-o" /> {{ reservation.departureSpot }}
              </div>
            </div>
            
            <div class="checkin-status">
              <van-button 
                v-if="reservation.state === '0'"
                type="primary"
                size="small"
                @click="checkIn(reservation)"
                :disabled="!canCheckIn(reservation)"
              >
                {{ getCheckInButtonText(reservation) }}
              </van-button>
              
              <div v-else-if="reservation.state === '1'" class="completed-status">
                <van-icon name="checked" color="#07c160" />
                <span>已签到</span>
                <div class="checkin-time">{{ reservation.signTime }}</div>
              </div>
              
              <van-tag v-else :type="getStatusTagType(reservation.state)">
                {{ getStatusText(reservation.state) }}
              </van-tag>
            </div>
          </div>
        </div>

        <van-empty 
          v-else
          description="今日无预约班车"
        >
          <van-button 
            type="primary" 
            size="small"
            @click="$router.push('/')"
          >
            立即预约
          </van-button>
        </van-empty>
      </div>

      <!-- 扫码签到 -->
      <div class="qr-checkin card">
        <div class="section-title">
          <van-icon name="scan" /> 扫码签到
        </div>
        <div class="qr-content">
          <van-button 
            type="primary" 
            block
            icon="scan"
            @click="startQRScan"
          >
            扫描二维码签到
          </van-button>
          <p class="qr-tip">请扫描车上的二维码进行签到确认</p>
        </div>
      </div>
    </div>

    <!-- 签到成功弹窗 -->
    <van-dialog
      v-model:show="showSuccessDialog"
      title="签到成功"
      :message="successMessage"
      confirm-button-text="确定"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useBusStore } from '../stores/bus.js'
import { showToast, showConfirmDialog } from 'vant'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import dingTalk from '../utils/dingtalk.js'
import { reservationApi } from '../api/reservation.js'

dayjs.locale('zh-cn')

const route = useRoute()
const router = useRouter()
const busStore = useBusStore()

// 响应式数据
const currentTime = ref('')
const currentDate = ref('')
const showSuccessDialog = ref(false)
const successMessage = ref('')
const myReservations = ref([])
const loading = ref(false)
const error = ref('')
let timeInterval = null

// 计算属性
const todayReservations = computed(() => {
  return myReservations.value
    .sort((a, b) => (a.busSchedules?.busTime || '').localeCompare(b.busSchedules?.busTime || '') || 0)
})

// 方法
// 更新当前时间和日期显示
const updateTime = () => {
  const now = dayjs()
  currentTime.value = now.format('HH:mm:ss')
  currentDate.value = now.format('YYYY年MM月DD日 dddd')
}

// 获取状态对应的文本显示
const getStatusText = (state) => {
  // 状态码映射：0-已预约(confirmed)，1-已完成(completed)，2-已取消(cancelled)
  const statusMap = {
    0: '已确认',
    1: '已完成',
    2: '已取消'
  }
  return statusMap[state] || '未知'
}

// 获取状态对应的标签样式类型
const getStatusTagType = (state) => {
  const typeMap = {
    0: 'primary',
    1: 'success',
    2: 'default'
  }
  return typeMap[state] || 'default'
}

// 判断是否可以进行签到
const canCheckIn = (reservation) => {
  // 检查预约是否存在且状态为已确认(0)
  if (!reservation || reservation.state !== 0) return false
  
  const now = dayjs()
  const busTime = dayjs(`${reservation.date} ${reservation.busSchedules?.time}`)
  
  // 只能在班车发车前30分钟到发车后30分钟内签到
  const checkInStart = busTime.subtract(30, 'minute')
  const checkInEnd = busTime.add(30, 'minute')
  
  return now.isAfter(checkInStart) && now.isBefore(checkInEnd)
}

// 获取签到按钮显示的文本
const getCheckInButtonText = (reservation) => {
  // 检查预约是否存在且状态为已确认(0)
  if (!reservation || reservation.state !== '0') {
    return '无法签到'
  }
  
  // 根据签到时间判断显示不同文本
  if (!canCheckIn(reservation)) {
    const now = dayjs()
    const busTime = dayjs(`${reservation.date} ${reservation.busSchedules?.time}`)
    
    if (now.isBefore(busTime.subtract(30, 'minute'))) {
      return '未到签到时间'
    } else {
      return '签到时间已过'
    }
  }
  return '立即签到'
}

// 获取班车时间
const getBusTime = (reservation) => {
  return reservation.busSchedules?.busTime || ''
}

// 获取班车路线
const getBusRoute = (reservation) => {
  if (reservation.busSchedules) {
    return `${reservation.busSchedules.start_station} - ${reservation.busSchedules.end_station}`
  }
  return ''
}

// 获取今日预约数据
const fetchMyReservations = async () => {
  try {
    loading.value = true
    error.value = ''
    
    // 获取用户手机号
    const phone = busStore.userInfo?.user?.phone || ''
    if (!phone) {
      showToast('未获取到用户信息')
      return
    }
    
    // 调用接口获取今日预约数据
    const params = {
      phone,
      date: dayjs().format('YYYY-MM-DD'),
      // state: 0 // 获取所有状态的预约
    }
    
    const response = await reservationApi.list(params)
    if (response && Array.isArray(response) && response.length > 0) {
      myReservations.value = response
    } else {
      myReservations.value = []
    }
  } catch (err) {
    console.error('获取预约数据失败:', err)
    error.value = '获取预约数据失败，请稍后重试'
    showToast(error.value)
  } finally {
    loading.value = false
  }
}

// 执行签到操作
const checkIn = async (reservation) => {
  // 验证预约信息
  if (!reservation) {
    showToast('预约信息错误')
    return
  }
  
  // 验证预约状态
  if (reservation.state !== 0) {
    showToast('该预约已无法签到')
    return
  }
  
  // 验证签到时间
  if (!canCheckIn(reservation)) {
    showToast('不在签到时间范围内')
    return
  }

  // 执行具体签到操作
  await performCheckIn(reservation)
}

// 启动扫码签到
const startQRScan = async () => {
  try {
    // 检查是否在钉钉环境中
    if (!dingTalk.isDingTalkEnv()) {
      // 非钉钉环境，使用模拟数据
      showToast('当前非钉钉环境，使用模拟扫码')
      setTimeout(() => {
        const mockQRData = 'bus_checkin_' + Date.now()
        handleQRResult(mockQRData)
      }, 1000)
      return
    }

    // 钉钉环境，调用真实扫码功能
    showToast('正在启动扫码...')
    const scanResult = await dingTalk.scanQRCode()

    if (scanResult.success) {
      handleQRResult(scanResult.text)
    } else {
      showToast('扫码取消')
    }
  } catch (error) {
    console.error('扫码失败:', error)
    showToast('扫码失败，请重试')
  }
}

// 处理扫码结果
const handleQRResult = async (qrData) => {
  try {
    console.log('QR扫描结果:', qrData)

    // 解析二维码数据
    // 预期格式: bus_checkin_[busId]_[date] 或 bus_checkin_[timestamp]
    if (!qrData.startsWith('bus_checkin_')) {
      showToast('无效的签到二维码')
      return
    }

    // 查找今日的预约记录
    const todayReservationsList = todayReservations.value.filter(r => r.state === '0')

    if (todayReservationsList.length === 0) {
      showToast('今日无可签到的预约记录')
      return
    }

    // 如果只有一个预约，直接签到
    if (todayReservationsList.length === 1) {
      const reservation = todayReservationsList[0]
      if (canCheckIn(reservation)) {
        await performCheckIn(reservation, qrData)
      } else {
        showToast('当前不在签到时间范围内')
      }
      return
    }

    // 多个预约，让用户选择
    showToast('检测到多个预约记录，请手动选择签到')
  } catch (error) {
    console.error('处理二维码失败:', error)
    showToast('签到失败，请重试')
  }
}

// 执行签到确认和请求
const performCheckIn = async (reservation, qrData = '') => {
  try {
    // 使用vant的Dialog组件确认签到
    const confirmed = await showConfirmDialog({
      title: '确认签到',
      message: `班车：${getBusTime(reservation)} ${getBusRoute(reservation)}
站点：${reservation.departureSpot || ''}
${qrData ? '扫码' : '手动'}签到`
    })
      .then(() => true)
      .catch(() => false)

    if (confirmed) {
      // 调用接口进行签到
      const params = {
        id: reservation.id,
      }
      
      const response = await reservationApi.sign(params)
      
      if (response && response.code === 200) {
        // 更新本地数据
        const index = myReservations.value.findIndex(r => r.id === reservation.id)
        if (index !== -1) {
          myReservations.value[index] = {
            ...myReservations.value[index],
            state: 1, // 已完成
            signTime: new Date().toISOString()
          }
        }
        
        // 使用钉钉成功提示
        await dingTalk.showSuccessToast('签到成功！')

        // 更新成功消息
        successMessage.value = `签到成功！\n班车：${getBusTime(reservation)} ${getBusRoute(reservation)}\n签到时间：${dayjs().format('HH:mm:ss')}`
        showSuccessDialog.value = true
      } else {
        throw new Error(response?.message || '签到失败')
      }
    }
  } catch (error) {
    console.error('签到失败:', error)
    await dingTalk.showErrorToast(error.message || '签到失败，请重试')
  }
}

// 页面挂载时执行
onMounted(async () => {
  // 更新时间并设置定时器
  updateTime()
  timeInterval = setInterval(updateTime, 1000)

  // 初始化钉钉SDK
  try {
    await dingTalk.init()
    if (dingTalk.isDingTalkEnv()) {
      // 设置钉钉导航栏
      await dingTalk.setNavigation({
        title: '签到确认',
        showBack: true
      })
    }
  } catch (error) {
    console.error('钉钉SDK初始化失败:', error)
  }

  // 获取今日预约数据
  await fetchMyReservations()

  // 如果从路由参数传入了预约ID，直接显示签到确认
  const { reservationId } = route.query
  if (reservationId) {
    const reservation = myReservations.value.find(r => r.id === Number(reservationId))
    if (reservation && reservation.state === '0') {
      checkIn(reservation)
    }
  }
})

// 页面卸载时执行
onUnmounted(() => {
  // 清除定时器
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
.checkin-page {
  width: 100%;
  min-height: 100%;
  background: #f7f8fa;
  overflow-x: hidden;
}

.time-card {
  text-align: center;
  margin-bottom: 16px;
}

.current-time {
  font-size: 32px;
  font-weight: 600;
  color: #1989fa;
  margin-bottom: 8px;
}

.current-date {
  font-size: 14px;
  color: #646566;
}

.bus-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.bus-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f7f8fa;
  border-radius: 8px;
  transition: all 0.2s;
}

.bus-item.completed {
  background: #f0f9ff;
  border: 1px solid #e1f5fe;
}

.bus-info {
  flex: 1;
}

.bus-time {
  font-size: 18px;
  font-weight: 600;
  color: #1989fa;
  margin-bottom: 4px;
}

.bus-route {
  font-size: 14px;
  color: #646566;
  margin-bottom: 4px;
}

.bus-station {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #969799;
}

.checkin-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.completed-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.completed-status span {
  font-size: 12px;
  color: #07c160;
}

.checkin-time {
  font-size: 10px;
  color: #969799;
}

.qr-content {
  text-align: center;
}

.qr-tip {
  margin-top: 12px;
  font-size: 12px;
  color: #969799;
  line-height: 1.4;
}
</style>
