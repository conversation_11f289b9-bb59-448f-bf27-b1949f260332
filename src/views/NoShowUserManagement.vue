<template>
  <div class="noshow-user-management-page">
    <div class="page-container">
      <!-- 爽约统计概览 -->
      <div class="stats-overview card">
        <div class="section-title">
          <van-icon name="bar-chart-o" />
          爽约统计概览
        </div>
        <van-grid :column-num="3" :border="false">
          <van-grid-item>
            <div class="stat-item">
              <div class="stat-number">{{ noShowStats.totalUsers }}</div>
              <div class="stat-label">爽约用户</div>
            </div>
          </van-grid-item>
          <van-grid-item>
            <div class="stat-item">
              <div class="stat-number">{{ noShowStats.restrictedUsers }}</div>
              <div class="stat-label">被限制用户</div>
            </div>
          </van-grid-item>
          <van-grid-item>
            <div class="stat-item">
              <div class="stat-number">{{ noShowStats.totalRecords }}</div>
              <div class="stat-label">总爽约次数</div>
            </div>
          </van-grid-item>
        </van-grid>
      </div>

      <!-- 筛选和操作 -->
      <div class="filter-actions card">
        <div class="filter-row">
          <van-field
            v-model="searchKeyword"
            placeholder="搜索用户姓名或电话"
            left-icon="search"
            clearable
          />
          <van-button 
            type="primary" 
            size="small"
            @click="refreshData"
            :loading="loading"
          >
            刷新
          </van-button>
        </div>
        
        <div class="filter-tabs">
          <van-tabs v-model:active="activeFilter" @change="onFilterChange">
            <van-tab title="全部" name="all"></van-tab>
            <van-tab title="已限制" name="restricted"></van-tab>
            <van-tab title="警告" name="warning"></van-tab>
          </van-tabs>
        </div>
      </div>

      <!-- 爽约用户列表 -->
      <div class="user-list-card card">
        <div class="section-title">
          <van-icon name="friends-o" />
          爽约用户列表 ({{ filteredUsers.length }})
        </div>
        
        <div class="user-list" v-if="filteredUsers.length > 0">
          <div 
            v-for="user in filteredUsers" 
            :key="user.userId"
            class="user-item"
            @click="viewUserDetail(user)"
          >
            <div class="user-avatar">
              <van-icon name="user-o" size="24" />
            </div>
            <div class="user-info">
              <div class="user-name">{{ user.userName }}</div>
              <div class="user-details">
                {{ user.userType === 'teacher' ? '教师' : '学生' }} | {{ user.department }}
              </div>
              <div class="user-contact">
                <van-icon name="phone-o" size="12" />
                {{ user.phone }}
              </div>
            </div>
            <div class="user-status">
              <van-tag 
                :type="getStatusTagType(user)"
                size="medium"
              >
                {{ user.noShowCount }}次爽约
              </van-tag>
              <div class="status-text" :class="getStatusClass(user)">
                {{ getStatusText(user) }}
              </div>
              <div class="last-noshow" v-if="user.lastNoShowDate">
                最近：{{ formatDate(user.lastNoShowDate) }}
              </div>
            </div>
            <van-icon name="arrow" />
          </div>
        </div>

        <van-empty 
          v-else-if="!loading"
          description="暂无爽约用户"
        />

        <van-loading v-if="loading" type="spinner" color="#1989fa">加载中...</van-loading>
      </div>

      <!-- 批量操作 -->
      <div class="batch-actions card" v-if="selectedUsers.length > 0">
        <div class="section-title">
          <van-icon name="setting-o" />
          批量操作 (已选择{{ selectedUsers.length }}个用户)
        </div>
        <div class="action-buttons">
          <van-button 
            type="warning" 
            size="small"
            @click="batchRemoveRestriction"
          >
            批量解除限制
          </van-button>
          <van-button 
            type="danger" 
            size="small"
            @click="batchAddRestriction"
          >
            批量限制预约
          </van-button>
          <van-button 
            size="small"
            @click="clearSelection"
          >
            取消选择
          </van-button>
        </div>
      </div>
    </div>

    <!-- 用户详情弹窗 -->
    <van-popup 
      v-model:show="showUserDetail" 
      position="bottom" 
      :style="{ height: '80%' }"
    >
      <div class="user-detail-popup" v-if="selectedUser">
        <div class="popup-header">
          <h3>{{ selectedUser.userName }} - 详细信息</h3>
          <div class="header-actions">
            <van-button 
              :type="selectedUser.isRestricted ? 'success' : 'danger'"
              size="small"
              @click="toggleUserRestriction"
              :loading="processingRestriction"
            >
              {{ selectedUser.isRestricted ? '解除限制' : '限制预约' }}
            </van-button>
            <van-button 
              size="small"
              @click="showUserDetail = false"
            >
              关闭
            </van-button>
          </div>
        </div>
        
        <!-- 用户基本信息 -->
        <div class="user-basic-info">
          <h4>基本信息</h4>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">姓名：</span>
              <span class="value">{{ selectedUser.userName }}</span>
            </div>
            <div class="info-item">
              <span class="label">身份：</span>
              <span class="value">{{ selectedUser.userType === 'teacher' ? '教师' : '学生' }}</span>
            </div>
            <div class="info-item">
              <span class="label">部门：</span>
              <span class="value">{{ selectedUser.department }}</span>
            </div>
            <div class="info-item">
              <span class="label">电话：</span>
              <span class="value">{{ selectedUser.phone }}</span>
            </div>
            <div class="info-item">
              <span class="label">爽约次数：</span>
              <span class="value danger">{{ selectedUser.noShowCount }} 次</span>
            </div>
            <div class="info-item">
              <span class="label">当前状态：</span>
              <span class="value" :class="getStatusClass(selectedUser)">
                {{ getStatusText(selectedUser) }}
              </span>
            </div>
          </div>
        </div>
        
        <!-- 爽约记录列表 -->
        <div class="user-records">
          <h4>爽约记录 ({{ selectedUser.records.length }}条)</h4>
          <div class="records-list">
            <div 
              v-for="record in selectedUser.records" 
              :key="record.id"
              class="record-item"
            >
              <div class="record-info">
                <div class="record-date">{{ formatDate(record.date) }}</div>
                <div class="record-bus">{{ getBusInfo(record.busId) }}</div>
                <div class="record-time">记录时间：{{ formatDateTime(record.recordTime) }}</div>
              </div>
              <div class="record-actions">
                <van-button 
                  size="mini" 
                  type="danger" 
                  @click="removeRecord(record.id)"
                >
                  删除
                </van-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 确认对话框 -->
    <van-dialog
      v-model:show="showConfirmDialog"
      :title="confirmTitle"
      :message="confirmMessage"
      show-cancel-button
      @confirm="confirmAction"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useBusStore } from '../stores/bus.js'
import { showToast } from 'vant'
import dayjs from 'dayjs'
import { initMockData } from '../utils/mockData.js'

const router = useRouter()
const busStore = useBusStore()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const activeFilter = ref('all')
const selectedUsers = ref([])
const showUserDetail = ref(false)
const selectedUser = ref(null)
const processingRestriction = ref(false)
const showConfirmDialog = ref(false)
const confirmTitle = ref('')
const confirmMessage = ref('')
const confirmCallback = ref(null)

// 计算属性
const myReservations = computed(() => busStore.myReservations)

const noShowUsers = computed(() => {
  const users = new Map()
  
  // 统计每个用户的爽约记录
  busStore.noShowRecords.forEach(record => {
    const reservation = myReservations.value.find(r => r.id === record.reservationId)
    if (reservation) {
      const userId = reservation.userName
      if (!users.has(userId)) {
        users.set(userId, {
          userId,
          userName: reservation.userName,
          userType: reservation.userType,
          department: reservation.userDepartment,
          phone: reservation.phone,
          records: [],
          noShowCount: 0,
          isRestricted: false,
          lastNoShowDate: null
        })
      }
      
      const user = users.get(userId)
      user.records.push(record)
      user.noShowCount = user.records.length
      user.isRestricted = user.noShowCount >= 3
      
      // 找到最近的爽约日期
      const recordDate = dayjs(record.recordTime)
      if (!user.lastNoShowDate || recordDate.isAfter(dayjs(user.lastNoShowDate))) {
        user.lastNoShowDate = record.recordTime
      }
    }
  })
  
  return Array.from(users.values()).filter(user => user.noShowCount > 0)
})

const filteredUsers = computed(() => {
  let users = noShowUsers.value
  
  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    users = users.filter(user => 
      user.userName.toLowerCase().includes(keyword) ||
      user.phone.includes(keyword)
    )
  }
  
  // 状态过滤
  if (activeFilter.value === 'restricted') {
    users = users.filter(user => user.isRestricted)
  } else if (activeFilter.value === 'warning') {
    users = users.filter(user => user.noShowCount >= 2 && !user.isRestricted)
  }
  
  return users.sort((a, b) => b.noShowCount - a.noShowCount)
})

const noShowStats = computed(() => {
  const users = noShowUsers.value
  return {
    totalUsers: users.length,
    restrictedUsers: users.filter(u => u.isRestricted).length,
    totalRecords: users.reduce((sum, u) => sum + u.noShowCount, 0)
  }
})

// 方法
const getStatusTagType = (user) => {
  if (user.isRestricted) return 'danger'
  if (user.noShowCount >= 2) return 'warning'
  return 'default'
}

const getStatusClass = (user) => {
  if (user.isRestricted) return 'danger'
  if (user.noShowCount >= 2) return 'warning'
  return 'success'
}

const getStatusText = (user) => {
  if (user.isRestricted) return '已限制预约'
  if (user.noShowCount >= 2) return '预警状态'
  return '正常'
}

const formatDate = (date) => {
  return dayjs(date).format('MM月DD日')
}

const formatDateTime = (datetime) => {
  return dayjs(datetime).format('MM月DD日 HH:mm')
}

const getBusInfo = (busId) => {
  const allSchedules = [
    ...busStore.busSchedules.weekday,
    ...busStore.busSchedules.weekend,
    ...busStore.busSchedules.vacation
  ]
  
  const bus = allSchedules.find(s => s.id === busId)
  return bus ? `${bus.time} ${bus.route}` : '未知班车'
}

const refreshData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    showToast('数据已刷新')
  } catch (error) {
    showToast('刷新失败')
  } finally {
    loading.value = false
  }
}

const onFilterChange = () => {
  // 筛选变化时的处理
}

const viewUserDetail = (user) => {
  selectedUser.value = user
  showUserDetail.value = true
}

const toggleUserRestriction = async () => {
  if (!selectedUser.value) return
  
  const action = selectedUser.value.isRestricted ? '解除限制' : '限制预约'
  confirmTitle.value = `确认${action}`
  confirmMessage.value = `确定要${action}用户"${selectedUser.value.userName}"吗？`
  confirmCallback.value = async () => {
    processingRestriction.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      selectedUser.value.isRestricted = !selectedUser.value.isRestricted
      showToast(`已${action}`)
    } catch (error) {
      showToast(`${action}失败`)
    } finally {
      processingRestriction.value = false
    }
  }
  showConfirmDialog.value = true
}

const removeRecord = async (recordId) => {
  confirmTitle.value = '删除爽约记录'
  confirmMessage.value = '确定要删除这条爽约记录吗？'
  confirmCallback.value = async () => {
    try {
      // 从用户记录中删除
      const recordIndex = selectedUser.value.records.findIndex(r => r.id === recordId)
      if (recordIndex > -1) {
        selectedUser.value.records.splice(recordIndex, 1)
        selectedUser.value.noShowCount = selectedUser.value.records.length
        selectedUser.value.isRestricted = selectedUser.value.noShowCount >= 3
      }
      
      // 从store中删除
      const storeIndex = busStore.noShowRecords.findIndex(r => r.id === recordId)
      if (storeIndex > -1) {
        busStore.noShowRecords.splice(storeIndex, 1)
      }
      
      showToast('记录已删除')
    } catch (error) {
      showToast('删除失败')
    }
  }
  showConfirmDialog.value = true
}

const batchRemoveRestriction = () => {
  showToast('批量解除限制功能开发中...')
}

const batchAddRestriction = () => {
  showToast('批量限制功能开发中...')
}

const clearSelection = () => {
  selectedUsers.value = []
}

const confirmAction = () => {
  if (confirmCallback.value) {
    confirmCallback.value()
    confirmCallback.value = null
  }
  showConfirmDialog.value = false
}

// 生命周期
onMounted(() => {
  initMockData(busStore)
})
</script>

<style scoped>
.noshow-user-management-page {
  width: 100%;
  min-height: 100%;
  background: #f7f8fa;
  overflow-x: hidden;
}

.filter-actions {
  margin-bottom: 12px;
}

.filter-row {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 12px;
}

.filter-row .van-field {
  flex: 1;
}

.user-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-item:active {
  background: #f7f8fa;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: #969799;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.user-details {
  font-size: 12px;
  color: #646566;
  margin-bottom: 4px;
}

.user-contact {
  font-size: 12px;
  color: #969799;
  display: flex;
  align-items: center;
  gap: 4px;
}

.user-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  margin-right: 8px;
}

.status-text {
  font-size: 12px;
}

.status-text.success {
  color: #07c160;
}

.status-text.warning {
  color: #ff976a;
}

.status-text.danger {
  color: #ee0a24;
}

.last-noshow {
  font-size: 10px;
  color: #c8c9cc;
}

.batch-actions {
  margin-top: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 用户详情弹窗样式 */
.user-detail-popup {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebedf0;
}

.popup-header h3 {
  margin: 0;
  font-size: 16px;
  color: #323233;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.user-basic-info {
  margin-bottom: 20px;
}

.user-basic-info h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #323233;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: #f7f8fa;
  border-radius: 4px;
}

.info-item .label {
  font-size: 12px;
  color: #646566;
}

.info-item .value {
  font-size: 12px;
  color: #323233;
  font-weight: 500;
}

.info-item .value.danger {
  color: #ee0a24;
}

.user-records {
  flex: 1;
  overflow-y: auto;
}

.user-records h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #323233;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f7f8fa;
  border-radius: 6px;
}

.record-info {
  flex: 1;
}

.record-date {
  font-size: 14px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 2px;
}

.record-bus {
  font-size: 12px;
  color: #646566;
  margin-bottom: 2px;
}

.record-time {
  font-size: 12px;
  color: #969799;
}
</style>
