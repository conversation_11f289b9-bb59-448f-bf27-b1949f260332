<template>
  <div class="reservation-page">
    <div class="page-container">
      <!-- 班车信息 -->
      <div class="bus-info-card card" v-if="selectedBus">
        <div class="bus-header">
          <div class="bus-time">{{ selectedBus.time }}</div>
          <van-tag type="primary">{{ selectedBus.route }}</van-tag>
        </div>
        <div class="bus-date">
          <van-icon name="calendar-o" />
          {{ formatDate(selectedDate) }}
        </div>
      </div>

      <!-- 预约表单 -->
      <div class="reservation-form card">
        <div class="section-title">预约信息</div>
        
        <van-form @submit="submitReservation">
          <!-- 日期选择 -->
          <van-field
            v-model="formData.dateText"
            name="date"
            label="乘车日期"
            placeholder="选择日期"
            readonly
            is-link
            @click="showDatePicker = true"
            :rules="[{ required: true, message: '请选择乘车日期' }]"
          />

          <!-- 班车选择 -->
          <van-field
            v-model="formData.busText"
            name="bus"
            label="班车班次"
            placeholder="选择班次"
            readonly
            is-link
            @click="showBusPicker = true"
            :rules="[{ required: true, message: '请选择班车班次' }]"
          />

          <!-- 上车站点选择 -->
          <van-field
            v-model="formData.stationText"
            name="station"
            label="上车站点"
            placeholder="选择上车站点"
            readonly
            is-link
            @click="showStationPicker = true"
            :rules="[{ required: true, message: '请选择上车站点' }]"
          >
            <template #button>
              <van-button
                size="mini"
                type="primary"
                @click.stop="selectQuickStation('滨江校区')"
                v-if="selectedBus && selectedBus.stations.includes('滨江校区')"
              >
                滨江校区
              </van-button>
            </template>
          </van-field>

          <!-- 联系电话 -->
          <van-field
            v-model="formData.phone"
            name="phone"
            label="联系电话"
            placeholder="请输入联系电话"
            :rules="[{ required: true, message: '请输入联系电话' }]"
          />

          <!-- 提交按钮 -->
          <div class="submit-section">
            <van-button 
              type="primary" 
              block 
              native-type="submit"
              :loading="submitting"
              :disabled="!canSubmit"
            >
              确认预约
            </van-button>
          </div>
        </van-form>
      </div>

      <!-- 预约须知 -->
      <div class="notice-card card">
        <div class="section-title">
          <van-icon name="info-o" />
          预约须知
        </div>
        <div class="notice-content">
          <p>1. 预约截止时间：乘车前一天18:00</p>
          <p>2. 请按时到达指定站点候车</p>
          <p>3. 连续3次爽约将被限制预约权限1周</p>
          <p>4. 如需取消预约，请提前在"我的预约"中操作</p>
        </div>
      </div>
    </div>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-picker
        :columns="dateColumns"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>

    <!-- 班车选择器 -->
    <van-popup v-model:show="showBusPicker" position="bottom">
      <van-picker
        :columns="busColumns"
        @confirm="onBusConfirm"
        @cancel="showBusPicker = false"
      />
    </van-popup>

    <!-- 站点选择器 -->
    <van-popup v-model:show="showStationPicker" position="bottom">
      <van-picker
        :columns="stationColumns"
        @confirm="onStationConfirm"
        @cancel="showStationPicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useBusStore } from '../stores/bus.js'
import { showToast, showConfirmDialog } from 'vant'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()
const busStore = useBusStore()

// 响应式数据
const showDatePicker = ref(false)
const showBusPicker = ref(false)
const showStationPicker = ref(false)
const submitting = ref(false)

const selectedDate = ref('')
const selectedBus = ref(null)

const formData = ref({
  dateText: '',
  busText: '',
  stationText: '',
  phone: '',
  date: '',
  busId: '',
  station: ''
})



// 计算属性
const availableDates = computed(() => busStore.getAvailableDates)
const userInfo = computed(() => busStore.userInfo)

const dateColumns = computed(() => {
  return availableDates.value.map(date => ({
    text: `${date.dateText} ${date.dayText}`,
    value: date.date
  }))
})

const busColumns = computed(() => {
  if (!selectedDate.value) return []
  const dateInfo = availableDates.value.find(d => d.date === selectedDate.value)
  if (!dateInfo) return []
  
  return dateInfo.schedules.map(schedule => ({
    text: `${schedule.time} ${schedule.route}`,
    value: schedule.id,
    schedule
  }))
})

const stationColumns = computed(() => {
  if (!selectedBus.value) return []
  return selectedBus.value.stations.map(station => ({
    text: station,
    value: station
  }))
})

const canSubmit = computed(() => {
  return formData.value.date && 
         formData.value.busId && 
         formData.value.station && 
         formData.value.phone &&
         !busStore.isUserRestricted
})

// 方法
const formatDate = (date) => {
  return dayjs(date).format('YYYY年MM月DD日')
}

const onDateConfirm = ({ selectedOptions }) => {
  const option = selectedOptions[0]
  formData.value.dateText = option.text
  formData.value.date = option.value
  selectedDate.value = option.value
  
  // 清空班车和站点选择
  formData.value.busText = ''
  formData.value.busId = ''
  formData.value.stationText = ''
  formData.value.station = ''
  selectedBus.value = null
  
  showDatePicker.value = false
}

const onBusConfirm = ({ selectedOptions }) => {
  const option = selectedOptions[0]
  formData.value.busText = option.text
  formData.value.busId = option.value
  selectedBus.value = option.schedule
  
  // 清空站点选择
  formData.value.stationText = ''
  formData.value.station = ''
  
  showBusPicker.value = false
}

const onStationConfirm = ({ selectedOptions }) => {
  const option = selectedOptions[0]
  formData.value.stationText = option.text
  formData.value.station = option.value
  showStationPicker.value = false
}

const selectQuickStation = (station) => {
  formData.value.station = station
  formData.value.stationText = station
}

const submitReservation = async () => {
  if (busStore.isUserRestricted) {
    showToast('您因连续爽约已被限制预约，请联系管理员')
    return
  }

  try {
    submitting.value = true
    
    const reservation = {
      date: formData.value.date,
      busId: formData.value.busId,
      busTime: selectedBus.value.time,
      busRoute: selectedBus.value.route,
      station: formData.value.station,
      phone: formData.value.phone,
      userName: userInfo.value.name,
      userDepartment: userInfo.value.department
    }
    
    const result = busStore.addReservation(reservation)
    
    showToast('预约成功！')
    
    // 跳转到我的预约页面
    setTimeout(() => {
      router.push('/my-reservations')
    }, 1500)
    
  } catch (error) {
    showToast('预约失败，请重试')
    console.error('预约失败:', error)
  } finally {
    submitting.value = false
  }
}

// 生命周期
onMounted(() => {
  // 从路由参数获取预设值
  const { date, busId } = route.query
  
  if (date) {
    selectedDate.value = date
    const dateInfo = availableDates.value.find(d => d.date === date)
    if (dateInfo) {
      formData.value.dateText = `${dateInfo.dateText} ${dateInfo.dayText}`
      formData.value.date = date
    }
  }
  
  if (busId && date) {
    const dateInfo = availableDates.value.find(d => d.date === date)
    if (dateInfo) {
      const bus = dateInfo.schedules.find(s => s.id === busId)
      if (bus) {
        selectedBus.value = bus
        formData.value.busText = `${bus.time} ${bus.route}`
        formData.value.busId = busId
      }
    }
  }
  
  // 预填用户电话
  if (userInfo.value.phone) {
    formData.value.phone = userInfo.value.phone
  }
})
</script>

<style scoped>
.reservation-page {
  width: 100%;
  min-height: 100%;
  background: #f7f8fa;
  overflow-x: hidden;
}

.bus-info-card {
  margin-bottom: 16px;
}

.bus-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.bus-time {
  font-size: 24px;
  font-weight: 600;
  color: #1989fa;
}

.bus-date {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #646566;
  font-size: 14px;
}

.reservation-form {
  margin-bottom: 16px;
}

.submit-section {
  margin-top: 24px;
}

.notice-card {
  margin-bottom: 16px;
}

.notice-content {
  color: #646566;
  font-size: 14px;
  line-height: 1.6;
}

.notice-content p {
  margin: 8px 0;
}
</style>
