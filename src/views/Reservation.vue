<template>
  <div class="reservation-page">
    <div class="page-container">
      <!-- 班车信息 -->
      <div class="bus-info-card card" v-if="selectedBus">
        <div class="bus-header">
          <div class="bus-time">{{ selectedBus.busTime }}</div>
          <span>{{ selectedBus.start_station }} → {{ selectedBus.end_station }}</span>
          <!-- <van-tag type="primary"></van-tag> -->
        </div>
        <div class="bus-date">
          <van-icon name="calendar-o" />
          {{ formatDate(selectedBus.busDate) }}
        </div>
      </div>

      <!-- 预约表单 -->
      <div class="reservation-form card">
        <div class="section-title">预约信息</div>
        
        <van-form @submit="submitReservation" :model="formData">
          <!-- 乘车日期 -->
          <van-field
            v-model="formData.dateText"
            name="date"
            label="乘车日期"
            placeholder="乘车日期"
            readonly
            :rules="[{ required: true, message: '请选择乘车日期' }]"
          />

          <!-- 班车选择 -->
          <van-field
            v-model="formData.busText"
            name="bus"
            label="班车班次"
            placeholder="选择班次"
            readonly
            :rules="[{ required: true, message: '请选择班车班次' }]"
          />

          <!-- 上车站点选择 -->
          <van-field
            v-model="formData.stationText"
            name="station"
            label="上车站点"
            placeholder="选择上车站点"
            readonly
            is-link
            @click="showStationPicker = true"
            :rules="[{ required: true, message: '请选择上车站点' }]"
          />

          <!-- 联系电话 -->
          <van-field
            v-model="formData.phone"
            name="phone"
            label="联系电话"
            placeholder="请输入联系电话"
            readonly
            :rules="[{ required: true, message: '请输入联系电话' }]"
          />

          <!-- 提交按钮 -->
          <div class="submit-section">
            <van-button 
              type="primary" 
              block 
              native-type="submit"
              :loading="submitting"
              :disabled="!canSubmit"
            >
              确认预约
            </van-button>
          </div>
        </van-form>
      </div>

      <!-- 预约须知 -->
      <div class="notice-card card">
        <div class="section-title">
          <van-icon name="info-o" />
          预约须知
        </div>
        <div class="notice-content">
          <p>1. 预约截止时间：乘车前一天18:00</p>
          <p>2. 请按时到达指定站点候车</p>
          <p>3. 连续3次爽约将被限制预约权限1周</p>
          <p>4. 如需取消预约，请提前在"我的预约"中操作</p>
        </div>
      </div>
    </div>

    <!-- 站点选择器 -->
    <van-popup v-model:show="showStationPicker" position="bottom">
      <van-picker
        :columns="stationColumns"
        @confirm="onStationConfirm"
        @cancel="showStationPicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useBusStore } from '../stores/bus.js'
import { showToast } from 'vant'
import dayjs from 'dayjs'
import { reservationApi } from '../api/reservation.js'

const route = useRoute()
const router = useRouter()
const busStore = useBusStore()

// 响应式数据
const showStationPicker = ref(false)
const submitting = ref(false)
const selectedBus = ref(null)

// 使用reactive替代ref对象，更好地组织表单数据
const formData = reactive({
  dateText: '',
  busText: '',
  stationText: '',
  phone: '',
  date: '',
  busId: '',
  station: ''
})

// 计算属性
const userInfo = computed(() => busStore.userInfo)
const isUserRestricted = computed(() => busStore.isUserRestricted)

// 优化站点列的计算逻辑
const stationColumns = computed(() => {
  if (!selectedBus.value || !selectedBus.value.station) return []
  
  // 确保station字段存在且为字符串
  const stationStr = String(selectedBus.value.station)
  
  // 处理站点数据，确保每个站点都有text和value
  return stationStr.split(',')
    .map(station => station.trim())
    .filter(station => station.length > 0)
    .map(station => ({
      text: station,
      value: station
    }))
})

// 优化提交条件的判断逻辑
const canSubmit = computed(() => {
  return selectedBus.value &&
         formData.date && 
         formData.busId && 
         formData.station && 
         formData.phone &&
         !isUserRestricted.value
})

// 工具方法
const formatDate = (date) => {
  return date ? dayjs(date).format('YYYY年MM月DD日') : ''
}

// 站点选择确认处理
const onStationConfirm = ({ selectedOptions }) => {
  if (selectedOptions && selectedOptions.length > 0) {
    const option = selectedOptions[0]
    formData.stationText = option.text
    formData.station = option.value
  }
  showStationPicker.value = false
}

// 初始化表单数据
const initFormData = (schedule) => {
  if (!schedule) return
  
  // 重置表单数据
  Object.assign(formData, {
    date: schedule.busDate,
    dateText: formatDate(schedule.busDate),
    busId: schedule.id,
    busText: `${schedule.busTime} ${schedule.start_station} → ${schedule.end_station}`,
    stationText: '',
    station: ''
  })
  
  // 预填用户电话
  if (userInfo.value?.user?.phone) {
    formData.phone = userInfo.value.user.phone
  }
}

// 提交预约，使用防抖处理
let submitTimer = null
const submitReservation = async () => {
  // 清除之前的定时器，防止重复提交
  if (submitTimer) {
    clearTimeout(submitTimer)
  }
  
  // 添加防抖，1000ms内只能提交一次
  submitTimer = setTimeout(async () => {
    if (isUserRestricted.value) {
      showToast('您因连续爽约已被限制预约，请联系管理员')
      return
    }

    try {
      submitting.value = true
      
      // 构建预约数据，直接使用selectedBus中的数据
      const reservationData = {
        name: userInfo.value.user?.nickName || '',
        phone: formData.phone,
        departureSpot: formData.station,
        busSchedules: {
          id: selectedBus.value.id,
          start_station: selectedBus.value.start_station,
          end_station: selectedBus.value.end_station,
          busTime: selectedBus.value.busTime,
          busDate: selectedBus.value.busDate,
          station: selectedBus.value.station
        },
        reservationDate: selectedBus.value.busDate,
      }
      
      // 调用预约API提交数据
      const result = await reservationApi.create(reservationData)
      console.log(result)
      // 检查提交结果
      console.log(result)
      showToast('预约成功！')
      // 跳转到我的预约页面
      setTimeout(() => {
        router.push('/my-reservations')
      }, 1500)
    } catch (error) {
      console.error('预约失败:', error)
    } finally {
      submitting.value = false
    }
  }, 300)
}

// 解析路由参数中的班次数据
const parseScheduleFromRoute = () => {
  const { schedule: scheduleStr } = route.query
  
  if (!scheduleStr) {
    showToast('请从首页选择班车后预约')
    setTimeout(() => {
      router.push('/')
    }, 1500)
    return null
  }
  
  try {
    const schedule = JSON.parse(scheduleStr)
    
    // 验证班次数据的有效性
    if (!schedule || typeof schedule !== 'object' || !schedule.busDate) {
      throw new Error('班次数据格式不正确或缺少日期信息')
    }
    
    return schedule
  } catch (error) {
    console.error('解析班次数据失败:', error)
    showToast('获取班车信息失败，请稍后重试')
    
    // 解析失败后跳转回首页
    setTimeout(() => {
      router.push('/')
    }, 1500)
    
    return null
  }
}

// 生命周期
onMounted(() => {
  // 从路由参数解析班次数据
  const schedule = parseScheduleFromRoute()
  
  if (schedule) {
    selectedBus.value = schedule
    initFormData(schedule)
  }
})
</script>

<style scoped>
.reservation-page {
  width: 100%;
  min-height: 100%;
  background: #f7f8fa;
  overflow-x: hidden;
}

.bus-info-card {
  margin-bottom: 16px;
}

.bus-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.bus-time {
  font-size: 24px;
  font-weight: 600;
  color: #1989fa;
}

.bus-date {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #646566;
  font-size: 14px;
}

.reservation-form {
  margin-bottom: 16px;
}

.submit-section {
  margin-top: 24px;
}

.notice-card {
  margin-bottom: 16px;
}

.notice-content {
  color: #646566;
  font-size: 14px;
  line-height: 1.6;
}

.notice-content p {
  margin: 8px 0;
}
</style>
