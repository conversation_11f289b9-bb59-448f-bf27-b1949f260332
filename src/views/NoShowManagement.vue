<template>
  <div class="noshow-management-page">
    <div class="page-container">
      <!-- 当前状态卡片 -->
      <div class="status-card card">
        <div class="section-title">
          <van-icon name="warning-o" />
          当前状态
        </div>
        
        <div class="status-info">
          <div class="status-item">
            <div class="status-label">累计爽约次数</div>
            <div class="status-value" :class="{ 'warning': noShowCount >= 2, 'danger': noShowCount >= 3 }">
              {{ noShowCount }} 次
            </div>
          </div>
          
          <div class="status-item">
            <div class="status-label">预约权限状态</div>
            <div class="status-value" :class="{ 'success': !isRestricted, 'danger': isRestricted }">
              {{ isRestricted ? '已限制' : '正常' }}
            </div>
          </div>
          
          <div class="status-item" v-if="isRestricted">
            <div class="status-label">解除限制时间</div>
            <div class="status-value danger">
              {{ restrictionEndTime }}
            </div>
          </div>
        </div>
        
        <!-- 限制倒计时 -->
        <div class="restriction-countdown" v-if="isRestricted">
          <van-count-down 
            :time="remainingTime" 
            format="DD 天 HH 时 mm 分 ss 秒"
            @finish="onCountdownFinish"
          />
          <p class="countdown-tip">限制解除倒计时</p>
        </div>
        
        <!-- 状态说明 -->
        <div class="status-notice">
          <van-notice-bar
            v-if="noShowCount >= 2 && noShowCount < 3"
            left-icon="warning-o"
            text="您已有2次爽约记录，再次爽约将被限制预约权限1周"
            color="#ff976a"
            background="#fff7cc"
          />
          <van-notice-bar
            v-else-if="isRestricted"
            left-icon="cross"
            text="您因连续3次爽约已被限制预约权限，请等待限制解除"
            color="#ee0a24"
            background="#ffeaea"
          />
          <van-notice-bar
            v-else
            left-icon="success"
            text="您的预约权限正常，请按时乘车避免爽约"
            color="#07c160"
            background="#f0f9ff"
          />
        </div>
      </div>

      <!-- 爽约记录列表 -->
      <div class="records-card card">
        <div class="section-title">
          <van-icon name="records" />
          爽约记录
        </div>
        
        <div class="records-list" v-if="noShowRecords.length > 0">
          <div 
            v-for="record in sortedNoShowRecords" 
            :key="record.id"
            class="record-item"
          >
            <div class="record-info">
              <div class="record-date">{{ formatDate(record.date) }}</div>
              <div class="record-bus">{{ getBusInfo(record) }}</div>
            </div>
          </div>
        </div>

        <van-empty 
          v-else
          description="暂无爽约记录"
          image="https://fastly.jsdelivr.net/npm/@vant/assets/custom-empty-image.png"
        >
          <p class="empty-tip">保持良好的乘车习惯，按时乘车</p>
        </van-empty>
      </div>

      <!-- 规则说明 -->
      <div class="rules-card card">
        <div class="section-title">
          <van-icon name="info-o" />
          爽约管理规则
        </div>
        
        <div class="rules-content">
          <div class="rule-item">
            <div class="rule-number">1</div>
            <div class="rule-text">预约后未按时乘车视为爽约</div>
          </div>
          <div class="rule-item">
            <div class="rule-number">2</div>
            <div class="rule-text">累计2次爽约将收到预警提醒</div>
          </div>
          <div class="rule-item">
            <div class="rule-number">3</div>
            <div class="rule-text">累计3次爽约将被限制预约权限1周</div>
          </div>
          <div class="rule-item">
            <div class="rule-number">4</div>
            <div class="rule-text">限制时间从第3次爽约当日18:00开始计算</div>
          </div>
          <div class="rule-item">
            <div class="rule-number">5</div>
            <div class="rule-text">每累计满3次爽约重新计算限制时间</div>
          </div>
        </div>
      </div>

      <!-- 申诉按钮 -->
      <div class="appeal-section" v-if="noShowCount > 0">
        <p class="appeal-tip">如有异议，可联系管理员申诉</p>
      </div>
    </div>

    <!-- 申诉对话框 -->
    <van-dialog
      v-model:show="showAppeal"
      title="申诉爽约记录"
      show-cancel-button
      @confirm="submitAppeal"
    >
      <div class="appeal-form">
        <van-field
          v-model="appealReason"
          type="textarea"
          placeholder="请详细说明申诉原因..."
          rows="4"
          maxlength="200"
          show-word-limit
        />
      </div>
    </van-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useBusStore } from '../stores/bus.js'
import { showToast } from 'vant'
import dayjs from 'dayjs'

const busStore = useBusStore()

// 响应式数据
const showAppeal = ref(false)
const appealReason = ref('')
const currentTime = ref(dayjs())
let timeInterval = null

// 计算属性
const noShowRecords = computed(() => {
  // 处理对象格式的 noShowRecords
  const records = busStore.noShowRecords
  // 如果是对象格式且包含 reservationsList，则提取 reservationsList 中的爽约记录
  if (records && typeof records === 'object' && records.reservationsList) {
    return records.reservationsList.filter(reservation => reservation.isRide === 'N')
  }
  // 否则保持原有数组处理
  return Array.isArray(records) ? records : []
})

const noShowCount = computed(() => {
  // 计算最近的爽约次数
  return noShowRecords.value.filter(record => {
    return dayjs().diff(dayjs(record.date), 'day') <= 30 // 30天内的记录
  }).length
})

const isRestricted = computed(() => {
  // 根据爽约次数判断是否被限制
  return noShowCount.value >= 3
})

const sortedNoShowRecords = computed(() => {
  return [...noShowRecords.value].sort((a, b) => 
    dayjs(b.date).valueOf() - dayjs(a.date).valueOf()
  )
})

const restrictionEndTime = computed(() => {
  if (!isRestricted.value || noShowRecords.value.length === 0) return ''
  
  // 找到最近的记录来计算限制结束时间
  const recentRecords = noShowRecords.value
    .filter(record => dayjs().diff(dayjs(record.date), 'day') <= 7)
    .sort((a, b) => dayjs(b.date).valueOf() - dayjs(a.date).valueOf())
  
  if (recentRecords.length >= 3) {
    const thirdRecord = recentRecords[2]
    const restrictionStart = dayjs(thirdRecord.date).hour(18).minute(0).second(0)
    const restrictionEnd = restrictionStart.add(7, 'day')
    return restrictionEnd.format('YYYY年MM月DD日 18:00')
  }
  
  return ''
})

const remainingTime = computed(() => {
  if (!isRestricted.value || !restrictionEndTime.value) return 0
  
  const endTime = dayjs(restrictionEndTime.value, 'YYYY年MM月DD日 HH:mm')
  const remaining = endTime.diff(currentTime.value)
  
  return remaining > 0 ? remaining : 0
})

// 方法
const formatDate = (date) => {
  return dayjs(date).format('YYYY年MM月DD日')
}

const formatDateTime = (datetime) => {
  return dayjs(datetime).format('MM月DD日 HH:mm')
}

const getBusInfo = (record) => {
  // 根据记录中的信息获取班车信息
  if (record.busSchedules) {
    return `${record.busSchedules.time} ${record.busSchedules.start_station}至${record.busSchedules.end_station}`
  }
  return '未知班车'
}

const showAppealDialog = () => {
  showAppeal.value = true
  appealReason.value = ''
}

const submitAppeal = () => {
  if (!appealReason.value.trim()) {
    showToast('请填写申诉原因')
    return
  }
  
  // 这里应该调用后端API提交申诉
  showToast('申诉已提交，请等待管理员处理')
  showAppeal.value = false
  appealReason.value = ''
}

const onCountdownFinish = () => {
  showToast('预约权限已恢复！')
  // 刷新页面状态
  location.reload()
}

const updateCurrentTime = () => {
  currentTime.value = dayjs()
}

// 生命周期
onMounted(() => {
  // 每秒更新当前时间，用于倒计时计算
  timeInterval = setInterval(updateCurrentTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
.noshow-management-page {
  width: 100%;
  min-height: 100%;
  background: #f7f8fa;
  overflow-x: hidden;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.status-label {
  font-size: 14px;
  color: #646566;
}

.status-value {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.status-value.success {
  color: #07c160;
}

.status-value.warning {
  color: #ff976a;
}

.status-value.danger {
  color: #ee0a24;
}

.restriction-countdown {
  text-align: center;
  padding: 16px;
  background: #fff2f0;
  border-radius: 8px;
  margin-bottom: 16px;
}

.countdown-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #ee0a24;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f7f8fa;
  border-radius: 8px;
}

.record-info {
  flex: 1;
}

.record-date {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.record-bus {
  font-size: 14px;
  color: #646566;
}


.rules-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rule-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.rule-number {
  width: 20px;
  height: 20px;
  background: #1989fa;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.rule-text {
  flex: 1;
  font-size: 14px;
  color: #646566;
  line-height: 1.5;
}

.appeal-section {
  text-align: center;
  margin-top: 16px;
}

.appeal-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #969799;
}

.appeal-form {
  padding: 16px;
}

.empty-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #969799;
}
</style>
