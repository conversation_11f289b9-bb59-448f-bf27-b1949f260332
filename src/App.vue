<script setup>
import { useRouter, useRoute } from 'vue-router'
import { ref, watch } from 'vue'

const router = useRouter()
const route = useRoute()

// 页面标题
const pageTitle = ref('校车预约')

// 监听路由变化更新标题
watch(route, (to) => {
  const newTitle = to.meta?.title || '校车预约'
  pageTitle.value = newTitle
  document.title = newTitle

  // 如果在钉钉环境中，同步更新钉钉标题
  if (window.dd && window.dd.biz && window.dd.biz.navigation) {
    window.dd.biz.navigation.setTitle({
      title: newTitle
    }).catch(() => {
      // 忽略钉钉API调用失败
    })
  }
}, { immediate: true })
</script>

<template>
    <!-- 顶部导航栏 - 钉钉集成时不显示 -->
    <!-- <van-nav-bar
      :title="pageTitle"
      left-arrow
      @click-left="$router.go(-1)"
      v-if="$route.name !== 'Home'"
    /> -->

    <!-- 主要内容区域 -->
    <div class="main-content">
      <router-view />
    </div>

    <!-- 底部导航栏 -->
    <van-tabbar
      v-model="activeTab"
      @change="onTabChange"
      fixed
      placeholder
    >
      <van-tabbar-item icon="home-o" to="/">首页</van-tabbar-item>
      <van-tabbar-item icon="calendar-o" to="/my-reservations">我的预约</van-tabbar-item>
      <van-tabbar-item icon="checked" to="/checkin">签到</van-tabbar-item>
      <van-tabbar-item icon="setting-o" to="/admin">管理</van-tabbar-item>
    </van-tabbar>
</template>

<script>
export default {
  data() {
    return {
      activeTab: 0
    }
  },
  methods: {
    onTabChange(index) {
      // 标签页切换逻辑
    }
  }
}
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #f7f8fa;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
}

#app {
  width: 100%;
  min-height: 100vh;
  padding: 0;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  position: relative;
  background-color: #f7f8fa;
}

.main-content {
  flex: 1;
  width: 100%;
  min-height: calc(100vh - 50px); /* 减去底部tabbar高度 */
  padding: 0;
  margin: 0;
  overflow-x: hidden;
  background-color: #f7f8fa;
}

/* 自定义样式 */
.page-container {
  width: 100%;
  padding: 12px;
  margin: 0;
  box-sizing: border-box;
  overflow-x: hidden;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 12px;
}

.card {
  width: 100%;
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  box-sizing: border-box;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  html, body {
    width: 100%;
    overflow-x: hidden;
  }

  #app {
    width: 100%;
    overflow-x: hidden;
  }

  .main-content {
    width: 100%;
    overflow-x: hidden;
  }

  .page-container {
    width: 100%;
    padding: 8px;
    overflow-x: hidden;
  }

  .card {
    width: 100%;
    padding: 12px;
    margin-bottom: 8px;
    border-radius: 6px;
    overflow-x: hidden;
  }

  .section-title {
    font-size: 16px;
    margin-bottom: 8px;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 375px) {
  .page-container {
    padding: 6px;
  }

  .card {
    padding: 10px;
    margin-bottom: 6px;
  }
}

/* 防止所有元素产生横向滚动 */
* {
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* 确保表单元素不超出容器 */
.van-field, .van-button, .van-cell {
  max-width: 100%;
  box-sizing: border-box;
}

/* 确保文本不会导致横向滚动 */
p, div, span, h1, h2, h3, h4, h5, h6 {
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}
</style>
