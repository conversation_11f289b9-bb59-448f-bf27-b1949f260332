# 校车预约服务 - 接口文档

## 基础信息

- **Base URL**: `/api`
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`
- **认证方式**: 钉钉OAuth（如有）

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": { /* 具体数据内容 */ }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "参数错误",
  "data": null
}
```

---

## 1. 预约管理接口

### 1.1 新增预约
- **接口地址**: `POST /api/reservation`
- **接口描述**: 新增校车预约
- **请求参数**:
```json
{
  "id": Long, // 预约ID
  "name": String, // 姓名
  "phone": String, // 手机号
  "departureSpot": String, // 上车地点
  "busSchedules": {
    "id": Long, // 班次ID
    "start_station": String, // 出发地
    "end_station": String, // 目的地
    "busTime": String, // 时间
    "busDate": String, // 日期
    "station": String, // 站点
  },
  "reservationDate": String, // 预约日期
  "isRide": String, // 签到（0:否，1:是）
  "state": String, // 状态（0:已预约，1:已完成，2:已取消）
  "signTime": String // 签到时间
}
```
- **响应**: 状态码 201，无内容

---

### 1.2 修改预约
- **接口地址**: `POST /api/reservation/put`
- **接口描述**: 修改校车预约
- **请求参数**:
```json
{
  "id": Long, // 预约ID
  "name": String, // 姓名
  "phone": String, // 手机号
  "dingUserId": String, // 钉钉用户ID
  "departureSpot": String, // 上车地点
  "busSchedules": {
    "id": Long, // 班次ID
    "start_station": String, // 出发地
    "end_station": String, // 目的地
    "busTime": String, // 时间
    "busDate": String, // 日期
    "station": String, // 站点
    "drivers": {
      "id": Long, // 驾驶员ID
      "name": String, // 驾驶员姓名
      "phone": String, // 驾驶员手机号
      "dingUserId": String, // 驾驶员钉钉ID
      "licensePlate": String // 车牌
    }
  },
  "reservationDate": String, // 预约日期
  "isRide": String, // 签到（0:否，1:是）
  "state": String, // 状态（0:已预约，1:已完成，2:已取消）
  "signTime": String // 签到时间
}
```
- **响应**: 状态码 200，无内容

---

### 1.3 删除预约
- **接口地址**: `POST /api/reservation/del`
- **接口描述**: 删除校车预约
- **请求参数**:
```json
{
  "id": Long // 预约ID
}
```
- **响应**: 状态码 200，无内容

---

### 1.4 查询预约列表
- **接口地址**: `GET /api/reservation`
- **接口描述**: 查询预约列表
- **请求参数**:
  - phone: String // 手机号
  - isRide: String // 签到（0:否，1:是）
  - state: String // 状态
  - date: String // 日期
  - schedulesId: Long // 班次ID
- **响应**:
```json
[
  {
    "id": Long, // 预约ID
    "name": String, // 姓名
    "phone": String, // 手机号
    "dingUserId": String, // 钉钉用户ID
    "departureSpot": String, // 上车地点
    "busSchedules": {
      "id": Long, // 班次ID
      "start_station": String, // 出发地
      "end_station": String, // 目的地
      "busTime": String, // 时间
      "busDate": String, // 日期
      "station": String, // 站点
      "drivers": {
        "id": Long, // 驾驶员ID
        "name": String, // 驾驶员姓名
        "phone": String, // 驾驶员手机号
        "dingUserId": String, // 驾驶员钉钉ID
        "licensePlate": String // 车牌
      }
    },
    "reservationDate": String, // 预约日期
    "isRide": String, // 签到（0:否，1:是）
    "state": String, // 状态（0:已预约，1:已完成，2:已取消）
    "signTime": String // 签到时间
  }
]
```

---

### 1.5 分页查询
- **接口地址**: `GET /api/reservation/queryAllpage`
- **接口描述**: 分页查询预约
- **请求参数**:
  - phone: String // 手机号
  - isRide: String // 签到（0:否，1:是）
  - state: String // 状态(0:已预约，1：已完成，2：已取消)
  - date: String // 日期
  - schedulesId: Long // 班次ID
  - page: Integer // 页码
  - size: Integer // 每页数量
- **响应**:
```json
{
  "content": [
    {
      "id": Long, // 预约ID
      "name": String, // 姓名
      "phone": String, // 手机号
      "dingUserId": String, // 钉钉用户ID
      "departureSpot": String, // 上车地点
      "busSchedules": {
        "id": Long, // 班次ID
        "start_station": String, // 出发地
        "end_station": String, // 目的地
        "busTime": String, // 时间
        "busDate": String, // 日期
        "station": String, // 站点
        "drivers": {
          "id": Long, // 驾驶员ID
          "name": String, // 驾驶员姓名
          "phone": String, // 驾驶员手机号
          "dingUserId": String, // 驾驶员钉钉ID
          "licensePlate": String // 车牌
        }
      },
      "reservationDate": String, // 预约日期
      "isRide": String, // 签到（0:否，1:是）
      "state": String, // 状态（0:已预约，1:已完成，2:已取消）
      "signTime": String // 签到时间
    }
  ],
  "totalElements": Integer, // 总条数
  "totalPages": Integer, // 总页数
  "page": Integer, // 当前页
  "size": Integer // 每页数量
}
```

---

### 1.6 签到
- **接口地址**: `POST /api/reservation/sign`
- **接口描述**: 预约签到
- **请求参数**:
```json
{
  "id": Long, // 预约ID
  "name": String, // 姓名
  "phone": String, // 手机号
  "dingUserId": String, // 钉钉用户ID
  "departureSpot": String, // 上车地点
  "busSchedules": {
    "id": Long, // 班次ID
    "start_station": String, // 出发地
    "end_station": String, // 目的地
    "busTime": String, // 时间
    "busDate": String, // 日期
    "station": String, // 站点
    "drivers": {
      "id": Long, // 驾驶员ID
      "name": String, // 驾驶员姓名
      "phone": String, // 驾驶员手机号
      "dingUserId": String, // 驾驶员钉钉ID
      "licensePlate": String // 车牌
    }
  },
  "reservationDate": String, // 预约日期
  "isRide": String, // 签到（0:否，1:是）
  "state": String, // 状态（0:已预约，1:已完成，2:已取消）
  "signTime": String // 签到时间
}
```
- **响应**: 状态码 201，无内容

---

### 1.7 统计
- **接口地址**: `GET /api/reservation/statistics`
- **接口描述**: 预约统计
- **请求参数**:
  - phone: String // 手机号
  - isRide: String // 签到（0:否，1:是）
  - state: String // 状态
  - date: String // 日期
  - schedulesId: Long // 班次ID
- **响应**:
```json
{
  "total": Integer, // 总预约数
  "checkedIn": Integer, // 已签到数
  "cancelled": Integer // 已取消数
}
```

---

### 1.8 推送车辆信息
- **接口地址**: `GET /api/reservation/informationPush`
- **接口描述**: 推送车辆信息
- **请求参数**:
  - phone: String // 手机号
  - isRide: String // 签到（0:否，1:是）
  - state: String // 状态
  - date: String // 日期
  - schedulesId: Long // 班次ID
- **响应**: 状态码 201，无内容

---

## 2. 班次管理接口

### 2.1 新增班次
- **接口地址**: `POST /api/schedules`
- **接口描述**: 新增班次
- **请求参数**:
```json
{
  "id": Long, // 班次ID
  "start_station": String, // 出发地
  "end_station": String, // 目的地
  "busTime": String, // 时间
  "busDate": String, // 日期
  "station": String, // 站点
  "drivers": {
    "id": Long, // 驾驶员ID
    "name": String, // 驾驶员姓名
    "phone": String, // 驾驶员手机号
    "dingUserId": String, // 驾驶员钉钉ID
    "licensePlate": String // 车牌
  }
}
```
- **响应**: 状态码 201，无内容

---

### 2.2 修改班次
- **接口地址**: `POST /api/schedules/put`
- **接口描述**: 修改班次
- **请求参数**:
```json
{
  "id": Long, // 班次ID
  "start_station": String, // 出发地
  "end_station": String, // 目的地
  "busTime": String, // 时间
  "busDate": String, // 日期
  "station": String, // 站点
  "drivers": {
    "id": Long, // 驾驶员ID
    "name": String, // 驾驶员姓名
    "phone": String, // 驾驶员手机号
    "dingUserId": String, // 驾驶员钉钉ID
    "licensePlate": String // 车牌
  }
}
```
- **响应**: 状态码 200，无内容

---

### 2.3 删除班次
- **接口地址**: `POST /api/schedules/del`
- **接口描述**: 删除班次
- **请求参数**:
```json
{
  "id": Long // 班次ID
}
```
- **响应**: 状态码 200，无内容

---

### 2.4 查询班次列表
- **接口地址**: `GET /api/schedules`
- **接口描述**: 查询班次列表
- **请求参数**:
  - busDate: String // 日期
- **响应**:
```json
[
  {
    "id": Long, // 班次ID
    "start_station": String, // 出发地
    "end_station": String, // 目的地
    "busTime": String, // 时间
    "busDate": String, // 日期
    "station": String, // 站点
    "drivers": {
      "id": Long, // 驾驶员ID
      "name": String, // 驾驶员姓名
      "phone": String, // 驾驶员手机号
      "dingUserId": String, // 驾驶员钉钉ID
      "licensePlate": String // 车牌
    }
  }
]
```

---

### 2.5 分页查询
- **接口地址**: `GET /api/schedules/queryAllpage`
- **接口描述**: 分页查询班次
- **请求参数**:
  - busDate: String // 日期
  - page: Integer // 页码
  - size: Integer // 每页数量
- **响应**:
```json
{
  "content": [
    {
      "id": Long, // 班次ID
      "start_station": String, // 出发地
      "end_station": String, // 目的地
      "busTime": String, // 时间
      "busDate": String, // 日期
      "station": String, // 站点
      "drivers": {
        "id": Long, // 驾驶员ID
        "name": String, // 驾驶员姓名
        "phone": String, // 驾驶员手机号
        "dingUserId": String, // 驾驶员钉钉ID
        "licensePlate": String // 车牌
      }
    }
  ],
  "totalElements": Integer, // 总条数
  "totalPages": Integer, // 总页数
  "page": Integer, // 当前页
  "size": Integer // 每页数量
}
```

---

### 2.6 安排驾驶员
- **接口地址**: `POST /api/schedules/arrangeDrivers`
- **接口描述**: 安排驾驶员
- **请求参数**:
```json
{
  "id": Long, // 班次ID
  "start_station": String, // 出发地
  "end_station": String, // 目的地
  "busTime": String, // 时间
  "busDate": String, // 日期
  "station": String, // 站点
  "drivers": {
    "id": Long, // 驾驶员ID
    "name": String, // 驾驶员姓名
    "phone": String, // 驾驶员手机号
  },
  "licensePlate": String // 车牌
}
```
- **响应**: 状态码 201，无内容

---

## 3. 驾驶员管理接口

### 3.1 新增驾驶员
- **接口地址**: `POST /api/drivers`
- **接口描述**: 新增驾驶员
- **请求参数**:
```json
{
  "id": Long, // 驾驶员ID
  "name": String, // 驾驶员姓名
  "phone": String, // 驾驶员手机号
  "dingUserId": String, // 驾驶员钉钉ID
  "licensePlate": String // 车牌
}
```
- **响应**: 状态码 201，无内容

---

### 3.2 修改驾驶员
- **接口地址**: `POST /api/drivers/put`
- **接口描述**: 修改驾驶员
- **请求参数**:
```json
{
  "id": Long, // 驾驶员ID
  "name": String, // 驾驶员姓名
  "phone": String, // 驾驶员手机号
  "dingUserId": String, // 驾驶员钉钉ID
  "licensePlate": String // 车牌
}
```
- **响应**: 状态码 200，无内容

---

### 3.3 删除驾驶员
- **接口地址**: `POST /api/drivers/del`
- **接口描述**: 删除驾驶员
- **请求参数**:
```json
{
  "id": Long // 驾驶员ID
}
```
- **响应**: 状态码 200，无内容

---

### 3.4 查询驾驶员列表
- **接口地址**: `GET /api/drivers`
- **接口描述**: 查询驾驶员列表
- **响应**:
```json
[
  {
    "id": Long, // 驾驶员ID
    "name": String, // 驾驶员姓名
    "phone": String, // 驾驶员手机号
  }
]
```

---

### 3.5 分页查询
- **接口地址**: `GET /api/drivers/queryAllpage`
- **接口描述**: 分页查询驾驶员
- **请求参数**:
  - phone: String // 手机号
  - date: String // 日期
  - page: Integer // 页码
  - size: Integer // 每页数量
- **响应**:
```json
{
  "content": [
    {
      "id": Long, // 驾驶员ID
      "name": String, // 驾驶员姓名
      "phone": String, // 驾驶员手机号
      "dingUserId": String, // 驾驶员钉钉ID
      "licensePlate": String // 车牌
    }
  ],
  "totalElements": Integer, // 总条数
  "totalPages": Integer, // 总页数
  "page": Integer, // 当前页
  "size": Integer // 每页数量
}
```

---

### 3.6 查询空闲驾驶员
- **接口地址**: `GET /api/drivers/notArranged`
- **接口描述**: 查询空闲驾驶员
- **请求参数**:
  - phone: String // 手机号
  - date: String // 日期
- **响应**:
```json
[
  {
    "id": Long, // 驾驶员ID
    "name": String, // 驾驶员姓名
    "phone": String, // 驾驶员手机号
    "dingUserId": String, // 驾驶员钉钉ID
    "licensePlate": String // 车牌
  }
]
```

---

## 4. 爽约管理接口

### 4.1 新增爽约记录
- **接口地址**: `POST /api/noshowrecords`
- **接口描述**: 新增爽约记录
- **请求参数**:
```json
{
  "id": Long, // 记录ID
  "name": String, // 姓名
  "phone": String, // 手机号
  "num": Integer, // 爽约次数
  "state": String, // 状态（0:不限制，1:限制）
  "limitedDate": String // 限制日期
}
```
- **响应**: 状态码 201，无内容

---

### 4.2 修改爽约记录
- **接口地址**: `POST /api/noshowrecords/put`
- **接口描述**: 修改爽约记录
- **请求参数**:
```json
{
  "id": Long, // 记录ID
  "name": String, // 姓名
  "phone": String, // 手机号
  "num": Integer, // 爽约次数
  "state": String, // 状态（0:不限制，1:限制）
  "limitedDate": String // 限制日期
}
```
- **响应**: 状态码 200，无内容

---

### 4.3 删除爽约记录
- **接口地址**: `POST /api/noshowrecords/del`
- **接口描述**: 删除爽约记录
- **请求参数**:
```json
{
  "id": Long // 记录ID
}
```
- **响应**: 状态码 200，无内容

---

### 4.4 查询爽约记录列表
- **接口地址**: `GET /api/noshowrecords`
- **接口描述**: 查询爽约记录列表
- **请求参数**:
  - phone: String // 手机号
  - isRide: String // 签到（0:否，1:是）
  - state: String // 状态
- **响应**:
```json
[
  {
    "id": Long, // 记录ID
    "name": String, // 姓名
    "phone": String, // 手机号
    "num": Integer, // 爽约次数
    "state": String, // 状态
    "limitedDate": String // 限制日期
  }
]
```

---

### 4.5 分页查询
- **接口地址**: `GET /api/noshowrecords/queryAllpage`
- **接口描述**: 分页查询爽约记录
- **请求参数**:
  - phone: String // 手机号
  - isRide: String // 签到（0:否，1:是）
  - state: String // 状态
  - page: Integer // 页码
  - size: Integer // 每页数量
- **响应**:
```json
{
  "content": [
    {
      "id": Long, // 记录ID
      "name": String, // 姓名
      "phone": String, // 手机号
      "num": Integer, // 爽约次数
      "state": String, // 状态
      "limitedDate": String // 限制日期
    }
  ],
  "totalElements": Integer, // 总条数
  "totalPages": Integer, // 总页数
  "page": Integer, // 当前页
  "size": Integer // 每页数量
}
```

---

### 4.6 我的爽约记录
- **接口地址**: `GET /api/noshowrecords/myRecords`
- **接口描述**: 查询当前用户爽约记录
- **请求参数**:
  - phone: String // 手机号
  - isRide: String // 签到（0:否，1:是）
  - state: String // 状态
- **响应**:
```json
[
  {
    "id": Long, // 记录ID
    "name": String, // 姓名
    "phone": String, // 手机号
    "num": Integer, // 爽约次数
    "state": String, // 状态
    "limitedDate": String // 限制日期
  }
]
```

---

### 4.7 统计
- **接口地址**: `GET /api/noshowrecords/statistics`
- **接口描述**: 爽约统计
- **请求参数**:
  - phone: String // 手机号
  - isRide: String // 签到（0:否，1:是）
  - state: String // 状态
- **响应**:
```json
{
 "userCount": Integer, // 爽约用户
  "TotalNumber": Integer, // 总爽约数
  "restrictionNum": Integer // 限制人数
}
```

---

## 5. 车辆管理接口

### 5.1 车辆信息查询
- **接口地址**: `get /api/busmanagement`
- **接口描述**: 车辆信息查询
- **响应**:
```json
[
  {
    "id": Long, // 记录ID
    "name": String, // 姓名
    "phone": String, // 手机号
    "num": Integer, // 爽约次数
    "state": String, // 状态
    "limitedDate": String // 限制日期
  }
]
```


---

