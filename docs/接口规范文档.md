# 两校区通勤车预约系统 - 接口文档

## 接口概述

本文档描述了两校区通勤车预约系统的后端API接口规范。所有接口均采用RESTful风格设计，使用JSON格式进行数据交换。

## 基础信息

- **Base URL**: `https://api.bus.example.com/v1`
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`
- **认证方式**: JWT Token / 钉钉OAuth

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据内容
  },
  "timestamp": 1640995200000
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "参数错误",
  "data": null,
  "timestamp": 1640995200000
}
```

### 状态码说明
- `200`: 请求成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 1. 用户管理接口

### 1.1 获取用户信息
- **接口地址**: `GET /user/info`
- **接口描述**: 获取当前登录用户信息
- **请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "user_001",
    "name": "张三",
    "phone": "13800138001",
    "type": "teacher",
    "department": "计算机学院",
    "isRestricted": false,
    "restrictionEndTime": null,
    "noShowCount": 0
  }
}
```

### 1.2 更新用户信息
- **接口地址**: `POST /user/update`
- **接口描述**: 更新用户基本信息

**请求参数**:
```json
{
  "name": "张三",
  "phone": "13800138001",
  "type": "teacher",
  "department": "计算机学院"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "用户信息更新成功",
  "data": null
}
```

### 1.3 钉钉用户授权
- **接口地址**: `POST /user/dingtalk/auth`
- **接口描述**: 通过钉钉授权码获取用户信息

**请求参数**:
```json
{
  "authCode": "dingtalk_auth_code_here",
  "corpId": "corp_id_here"
}
```

## 2. 班车时刻表接口

### 2.1 获取班车时刻表
- **接口地址**: `GET /schedule/list`
- **接口描述**: 获取班车时刻表信息

**请求参数**:
- `date` (可选): 查询日期，格式 YYYY-MM-DD
- `type` (可选): 时刻表类型 (weekday/weekend/vacation)

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "weekday": [
      {
        "id": "wd1",
        "time": "06:30",
        "route": "滨江至临安",
        "stations": ["滨江校区", "汽车西站", "五常站", "青山湖地铁口"],
        "capacity": 45,
        "available": true
      }
    ],
    "weekend": [...],
    "vacation": [...]
  }
}
```

### 2.2 获取可预约日期
- **接口地址**: `GET /schedule/available-dates`
- **接口描述**: 获取当前可预约的日期列表

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "date": "2024-01-15",
      "dateText": "明天",
      "weekday": "周一",
      "available": true
    },
    {
      "date": "2024-01-16",
      "dateText": "后天",
      "weekday": "周二",
      "available": true
    }
  ]
}
```

## 3. 预约管理接口

### 3.1 创建预约
- **接口地址**: `POST /reservation/create`
- **接口描述**: 创建新的班车预约

**请求参数**:
```json
{
  "date": "2024-01-15",
  "busId": "wd1",
  "station": "滨江校区",
  "phone": "13800138001"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "预约成功",
  "data": {
    "id": "reservation_001",
    "date": "2024-01-15",
    "busId": "wd1",
    "busTime": "06:30",
    "busRoute": "滨江至临安",
    "station": "滨江校区",
    "phone": "13800138001",
    "status": "confirmed",
    "createTime": "2024-01-14 15:30:00"
  }
}
```

### 3.2 获取我的预约
- **接口地址**: `GET /reservation/my-list`
- **接口描述**: 获取当前用户的预约记录

**请求参数**:
- `status` (可选): 预约状态筛选 (confirmed/completed/cancelled)
- `page` (可选): 页码，默认1
- `size` (可选): 每页数量，默认20

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": "reservation_001",
        "date": "2024-01-15",
        "busTime": "06:30",
        "busRoute": "滨江至临安",
        "station": "滨江校区",
        "phone": "13800138001",
        "status": "confirmed",
        "createTime": "2024-01-14 15:30:00",
        "checkInTime": null
      }
    ],
    "total": 10,
    "page": 1,
    "size": 20
  }
}
```

### 3.3 取消预约
- **接口地址**: `POST /reservation/cancel`
- **接口描述**: 取消指定的预约

**请求参数**:
```json
{
  "reservationId": "reservation_001"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "预约已取消",
  "data": null
}
```

## 4. 签到管理接口

### 4.1 签到确认
- **接口地址**: `POST /checkin/confirm`
- **接口描述**: 确认签到乘车

**请求参数**:
```json
{
  "reservationId": "reservation_001",
  "checkInType": "manual",
  "qrCode": null,
  "location": {
    "latitude": 30.2741,
    "longitude": 120.1551
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "签到成功",
  "data": {
    "reservationId": "reservation_001",
    "checkInTime": "2024-01-15 06:25:00",
    "checkInType": "manual"
  }
}
```

### 4.2 扫码签到
- **接口地址**: `POST /checkin/qr-scan`
- **接口描述**: 通过二维码扫码签到

**请求参数**:
```json
{
  "qrCode": "bus_checkin_wd1_20240115",
  "location": {
    "latitude": 30.2741,
    "longitude": 120.1551
  }
}
```

### 4.3 获取签到记录
- **接口地址**: `GET /checkin/records`
- **接口描述**: 获取用户签到记录

**请求参数**:
- `startDate` (可选): 开始日期
- `endDate` (可选): 结束日期
- `page` (可选): 页码
- `size` (可选): 每页数量

## 5. 爽约管理接口

### 5.1 获取爽约记录
- **接口地址**: `GET /noshow/my-records`
- **接口描述**: 获取当前用户的爽约记录

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": "noshow_001",
        "reservationId": "reservation_001",
        "date": "2024-01-10",
        "busId": "wd1",
        "busTime": "06:30",
        "busRoute": "滨江至临安",
        "recordTime": "2024-01-10 07:00:00",
        "reason": "系统自动记录"
      }
    ],
    "totalCount": 2,
    "isRestricted": false,
    "restrictionEndTime": null
  }
}
```

### 5.2 申诉爽约记录
- **接口地址**: `POST /noshow/appeal`
- **接口描述**: 申诉爽约记录

**请求参数**:
```json
{
  "recordId": "noshow_001",
  "reason": "当天身体不适，无法乘车"
}
```

## 6. 管理员接口

### 6.1 获取班车乘客名单
- **接口地址**: `GET /admin/passengers`
- **接口描述**: 获取指定班车的乘客名单

**请求参数**:
- `date`: 日期 (必填)
- `busId`: 班车ID (必填)

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "busInfo": {
      "id": "wd1",
      "time": "06:30",
      "route": "滨江至临安",
      "date": "2024-01-15"
    },
    "passengers": [
      {
        "id": "reservation_001",
        "userName": "张三",
        "userType": "teacher",
        "department": "计算机学院",
        "phone": "13800138001",
        "station": "滨江校区",
        "status": "confirmed",
        "checkInTime": null
      }
    ],
    "statistics": {
      "total": 15,
      "checkedIn": 0,
      "pending": 15
    }
  }
}
```

### 6.2 获取爽约用户列表
- **接口地址**: `GET /admin/noshow-users`
- **接口描述**: 获取所有爽约用户列表

**请求参数**:
- `status` (可选): 筛选状态 (all/restricted/warning)
- `keyword` (可选): 搜索关键词
- `page` (可选): 页码
- `size` (可选): 每页数量

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "users": [
      {
        "userId": "user_001",
        "userName": "张三",
        "userType": "teacher",
        "department": "计算机学院",
        "phone": "13800138001",
        "noShowCount": 2,
        "isRestricted": false,
        "lastNoShowDate": "2024-01-10",
        "records": [...]
      }
    ],
    "statistics": {
      "totalUsers": 5,
      "restrictedUsers": 1,
      "totalRecords": 12
    }
  }
}
```

### 6.3 管理用户限制状态
- **接口地址**: `POST /admin/user-restriction`
- **接口描述**: 限制或解除用户预约权限

**请求参数**:
```json
{
  "userId": "user_001",
  "action": "restrict",
  "reason": "连续爽约",
  "duration": 7
}
```

### 6.4 删除爽约记录
- **接口地址**: `POST /admin/noshow-record/delete`
- **接口描述**: 删除指定的爽约记录

**请求参数**:
```json
{
  "recordId": "noshow_001",
  "reason": "误判删除"
}
```

## 7. 统计报表接口

### 7.1 获取今日统计
- **接口地址**: `GET /statistics/today`
- **接口描述**: 获取今日运营统计数据

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "date": "2024-01-15",
    "totalReservations": 45,
    "checkedIn": 38,
    "noShow": 7,
    "activeBuses": 6,
    "busUtilization": 0.85
  }
}
```

### 7.2 导出数据
- **接口地址**: `GET /export/passengers`
- **接口描述**: 导出乘客名单Excel文件

**请求参数**:
- `date`: 日期
- `busId` (可选): 班车ID
- `format`: 导出格式 (excel/pdf)

**响应**: 文件下载

## 8. 消息通知接口

### 8.1 发送通知
- **接口地址**: `POST /notification/send`
- **接口描述**: 发送消息通知

**请求参数**:
```json
{
  "type": "reservation_confirm",
  "userId": "user_001",
  "content": {
    "title": "预约确认",
    "message": "您的班车预约已确认",
    "data": {
      "reservationId": "reservation_001"
    }
  }
}
```

## 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 1001 | 用户未登录 | 重新登录获取token |
| 1002 | 用户被限制预约 | 等待限制解除或联系管理员 |
| 2001 | 预约时间已过 | 选择其他可预约时间 |
| 2002 | 班车已满员 | 选择其他班次或时间 |
| 2003 | 重复预约 | 检查已有预约记录 |
| 3001 | 签到时间未到 | 等待签到时间窗口 |
| 3002 | 签到时间已过 | 联系管理员处理 |
| 4001 | 权限不足 | 联系管理员获取权限 |

## 接口调用示例

### JavaScript调用示例
```javascript
// 创建预约
const createReservation = async (data) => {
  try {
    const response = await fetch('/api/reservation/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(data)
    });
    
    const result = await response.json();
    if (result.code === 200) {
      console.log('预约成功', result.data);
    } else {
      console.error('预约失败', result.message);
    }
  } catch (error) {
    console.error('网络错误', error);
  }
};
```

## 接口版本管理

- **当前版本**: v1.0
- **版本策略**: 语义化版本控制
- **向后兼容**: 保证向后兼容性
- **废弃通知**: 提前30天通知接口废弃
