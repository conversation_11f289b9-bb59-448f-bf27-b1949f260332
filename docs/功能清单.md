# 两校区通勤车预约系统 - 功能清单

## 系统概述

两校区通勤车预约系统是一个基于Vue 3 + Vant的移动端H5应用，专为高校师生提供便捷的校车预约服务。系统集成钉钉平台，支持扫码签到、爽约管理等核心功能。

## 核心功能模块

### 1. 用户管理模块

#### 1.1 用户身份管理
对接学校统一用户中心，这里无需实现

#### 1.2 个人信息展示
- **功能描述**：在首页显示用户基本信息和状态
- **主要功能**：
  - 显示用户姓名、身份、部门
  - 显示个人爽约状态标签
  - 快速访问个人爽约记录
- **页面位置**：首页顶部
- **权限要求**：已登录用户

### 2. 班车预约模块

#### 2.1 快速预约
- **功能描述**：首页快速预约当前可预约的班车
- **主要功能**：
  - 显示今明两天可预约班车
  - 一键快速预约
  - 预约状态实时更新
- **页面位置**：首页快速预约区域
- **权限要求**：未被限制的用户

#### 2.2 详细预约
- **功能描述**：完整的班车预约流程
- **主要功能**：
  - 日期选择（后2天内）
  - 班车班次选择
  - 上车站点选择（支持常用站点快选）
  - 联系电话填写
  - 预约确认提交
- **页面位置**：预约详情页面 (`/reservation`)
- **权限要求**：未被限制的用户

#### 2.3 预约规则
- **时间限制**：只能预约后2天的班车
- **截止时间**：当日18:00为预约截止时间
- **状态管理**：预约直接确认，无需等待审核
- **限制机制**：爽约3次的用户被限制预约1周

### 3. 班车时刻表模块

#### 3.1 工作日班次
**滨江至临安方向**：
- 06:30 (滨江校区 → 汽车西站 → 五常站 → 青山湖地铁口)
- 11:50 (滨江校区 → 汽车西站 → 五常站 → 青山湖地铁口)
- 16:20 (滨江校区 → 汽车西站 → 五常站 → 青山湖地铁口)

**临安至滨江方向**：
- 12:10 (临安校区 → 青山湖地铁口 → 五常站 → 汽车西站)
- 17:00 (临安校区 → 青山湖地铁口 → 五常站 → 汽车西站)
- 21:20 (临安校区 → 青山湖地铁口 → 五常站 → 汽车西站)

#### 3.2 双休日班次（仅周日）
- 16:20 滨江至临安
- 18:30 临安至滨江

#### 3.3 寒暑假班次
- 07:30 滨江至临安
- 16:30 临安至滨江

### 4. 预约管理模块

#### 4.1 我的预约
- **功能描述**：查看和管理个人预约记录
- **主要功能**：
  - 预约记录列表展示
  - 按状态筛选（全部/已预约/已完成/已取消）
  - 预约详情查看
  - 取消预约操作
  - 快速跳转签到页面
- **页面位置**：我的预约页面 (`/my-reservations`)
- **权限要求**：已登录用户

#### 4.2 预约状态管理
- **已预约**：预约成功，等待乘车
- **已完成**：已签到确认乘车
- **已取消**：用户主动取消预约

### 5. 签到确认模块

#### 5.1 手动签到
- **功能描述**：用户手动确认乘车签到
- **主要功能**：
  - 显示今日预约班车列表
  - 签到时间窗口控制（发车前后30分钟）
  - 签到状态实时更新
  - 签到历史记录查看
- **页面位置**：签到页面 (`/checkin`)
- **权限要求**：有当日预约的用户

#### 5.2 扫码签到
- **功能描述**：通过钉钉扫码功能进行签到
- **主要功能**：
  - 调用钉钉扫码API
  - 二维码识别和验证
  - 自动匹配预约记录
  - 签到确认和状态更新
- **技术集成**：钉钉JS-SDK
- **权限要求**：钉钉环境 + 有当日预约

#### 5.3 签到规则
- **时间窗口**：班车发车前30分钟至发车后30分钟
- **签到方式**：手动签到 + 扫码签到
- **状态更新**：签到成功后预约状态变为"已完成"

### 6. 爽约管理模块

#### 6.1 个人爽约管理
- **功能描述**：用户查看和管理个人爽约记录
- **主要功能**：
  - 爽约记录列表展示
  - 当前爽约状态显示
  - 限制倒计时显示
  - 爽约规则说明
  - 申诉功能
- **页面位置**：爽约管理页面 (`/noshow-management`)
- **权限要求**：已登录用户

#### 6.2 爽约规则
- **记录规则**：预约后未按时乘车视为爽约
- **预警机制**：累计2次爽约收到预警提醒
- **限制机制**：累计3次爽约限制预约权限1周
- **时间计算**：限制时间从第3次爽约当日18:00开始

### 7. 系统管理模块

#### 7.1 班车数据管理
- **功能描述**：管理员查看班车运行数据
- **主要功能**：
  - 今日统计概览
  - 班车乘客名单查看
  - 按日期查询班车数据
  - 乘客信息导出
- **页面位置**：管理后台页面 (`/admin`)
- **权限要求**：管理员权限

#### 7.2 爽约用户管理
- **功能描述**：管理员专用的爽约用户管理系统
- **主要功能**：
  - 爽约统计概览
  - 爽约用户列表查看
  - 用户搜索和筛选
  - 用户详情和记录管理
  - 限制/解除限制操作
  - 删除错误爽约记录
- **页面位置**：爽约用户管理页面 (`/noshow-user-management`)
- **权限要求**：管理员权限

#### 7.3 数据统计
- **今日统计**：总预约、已签到、爽约、运行班次
- **爽约统计**：爽约用户数、被限制用户数、总爽约次数
- **数据导出**：乘客名单、统计报表

### 8. 钉钉集成模块

#### 8.1 用户身份集成
- **功能描述**：通过钉钉获取用户身份信息
- **主要功能**：
  - 钉钉用户信息获取
  - 身份验证和授权
  - 用户信息同步
- **技术实现**：钉钉JS-SDK

#### 8.2 扫码功能集成
- **功能描述**：集成钉钉扫码功能
- **主要功能**：
  - 调用钉钉扫码API
  - 二维码数据解析
  - 签到流程集成
- **技术实现**：dingtalk-jsapi

#### 8.3 消息通知集成
- **功能描述**：通过钉钉发送消息通知
- **主要功能**：
  - 预约确认通知
  - 签到提醒通知
  - 爽约警告通知
- **技术实现**：钉钉消息API

## 系统特色功能

### 1. 智能预约管理
- 自动时间控制，防止过期预约
- 智能站点推荐，提升用户体验
- 实时状态更新，信息同步及时

### 2. 完善的爽约管理
- 多层级爽约管理机制
- 自动限制和解除功能
- 管理员干预和申诉机制

### 3. 移动端优化
- 完美适配移动设备
- 响应式设计，支持多种屏幕
- 触摸友好的交互设计

### 4. 钉钉深度集成
- 无缝集成钉钉生态
- 扫码签到便捷高效
- 消息通知及时到达

## 技术架构

### 前端技术栈
- **框架**：Vue 3 (Composition API)
- **UI库**：Vant 4 (移动端组件库)
- **路由**：Vue Router 4
- **状态管理**：Pinia
- **HTTP请求**：Axios
- **日期处理**：Day.js
- **构建工具**：Vite

### 集成技术
- **钉钉集成**：dingtalk-jsapi
- **移动端适配**：Viewport + CSS媒体查询
- **PWA支持**：Service Worker (可选)

## 部署要求

### 环境要求
- Node.js 20.19+ 或 22.12+
- 现代浏览器支持
- HTTPS协议（钉钉要求）

### 钉钉配置
- 钉钉开发者账号
- H5微应用配置
- JS-SDK权限配置
- 域名白名单设置

## 后续扩展功能

### 计划功能
- [ ] 班车实时位置追踪
- [ ] 智能推荐最优班次
- [ ] 数据分析和报表系统
- [ ] 多语言支持
- [ ] 离线缓存功能
- [ ] 推送通知系统

### 技术优化
- [ ] 性能监控和优化
- [ ] 错误日志收集
- [ ] 自动化测试覆盖
- [ ] CI/CD部署流程
