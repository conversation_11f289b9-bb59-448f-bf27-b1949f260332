- **接口地址**: `GET /api/dingtalk/dingAuth`
- **请求参数**:
  - authCode: String // 钉钉免登返回的code
- **响应**:
```json
{
  "role": [
    {
        "dataScope": "全部",
        "id": 2,
        "level": 2,
        "name": "教师"
    },
        {
        "dataScope": "全部",
        "id": 2,
        "level": 2,
        "name": "校车管理员"
    },
  ],
  "user": {
    "accountNonExpired": true,
    "accountNonLocked": true,
    "authorities": [
      {
        "authority": "admin"
      }
    ],
    "credentialsNonExpired": true,
    "dataScopes": [],
    "enabled": true,
    "roles": [
      "admin"
    ],
    "user": {
      "concurDept": [],
      "createTime": "2025-01-10 14:28:04",
      "dept": {
        "deptSort": 1,
        "deptType": 0,
        "id": 10000,
        "name": "浙江省教育厅"
      },
      "deptId": 10000,
      "email": "<EMAIL>",
      "enabled": true,
      "gender": "男",
      "id": 1,
      "isAdmin": true,
      "jobs": [
        {
          "id": 11,
          "name": "运维测试"
        },
        {
          "id": 10,
          "name": "厅领导"
        }
      ],
      "name": "超级管理员",
      "nickName": "超级管理员",
      "password": "$2a$10$ohbLzjXULrXjwtspoSsX1u7T2qXbpBED.AvvJHGh6OSiCvrrRyQuC",
      "phone": "***********",
      "platformUserId": "123",
      "pwdResetTime": "2025-01-10 14:28:05",
      "roles": [
        {
          "dataScope": "全部",
          "id": 2,
          "level": 2,
          "name": "教师"
        },
         {
          "dataScope": "全部",
          "id": 2,
          "level": 2,
          "name": "校车管理员"
        },
      ],
      "updateTime": "2024-11-11 14:52:27",
      "updatedBy": "superAdmin",
      "userDuty": "测试222",
      "userType": 1,
      "username": "superAdmin"
    },
    "username": "superAdmin"
  },
  "value": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJqdGkiOiIxYmYzZTg4ZDExMTU0YTQwYjc2ODdjY2NhMWVlNTYzOCIsImF1dGgiOiJhZG1pbiIsInN1YiI6InN1cGVyQWRtaW4ifQ._a1sLNKnuU6DRQtfYsTSzvFBVYsUMjY30pyWlU4U_AWdbzCfdTz2EsYrm2Er3lOml5rcD87VhBQVROXV5SaUIA"
}
```