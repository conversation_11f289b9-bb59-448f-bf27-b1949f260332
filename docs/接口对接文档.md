
# 校车预约服务 - 接口文档


### 状态码说明
- `200`: 请求成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 1. 预约管理接口

### 1.1 新增预约
- **接口地址**: `POST /api/reservation`
- **请求体**:
```json
{
  "id": Long,
  "name": String,
  "phone": String,
  "dingUserId": String,
  "departureSpot": String,
  "busSchedules": {
    "id": Long,
    "start_station": String,
    "end_station": String,
    "time": String,
    "date": String,
    "station": String,
    "drivers": {
      "id": Long,
      "name": String,
      "phone": String,
      "dingUserId": String,
      "licensePlate": String
    }
  },
  "date": String,
  "isRide": String,
  "state": String,
  "signTime": String
}
```
- **响应**: 状态码 201，无内容

---

### 1.2 修改预约
- **接口地址**: `POST /api/reservation/put`
- **请求体**:
```json
{
  "id": Long,
  "name": String,
  "phone": String,
  "dingUserId": String,
  "departureSpot": String,
  "busSchedules": {
    "id": Long,
    "start_station": String,
    "end_station": String,
    "time": String,
    "date": String,
    "station": String,
    "drivers": {
      "id": Long,
      "name": String,
      "phone": String,
      "dingUserId": String,
      "licensePlate": String
    }
  },
  "date": String,
  "isRide": String,
  "state": String,
  "signTime": String
}
```
- **响应**: 状态码 200，无内容

---

### 1.3 删除预约
- **接口地址**: `POST /api/reservation/del`
- **请求体**:
```json
{
  "id": Long
}
```
- **响应**: 状态码 200，无内容

---

### 1.4 查询预约列表
- **接口地址**: `GET /api/reservation`
- **请求参数**:
  - phone: String
  - isRide: String
  - state: String
  - date: String
  - schedulesId: Long
- **响应**:
```json
[
  {
    "id": Long,
    "name": String,
    "phone": String,
    "dingUserId": String,
    "departureSpot": String,
    "busSchedules": {
      "id": Long,
      "start_station": String,
      "end_station": String,
      "time": String,
      "date": String,
      "station": String,
      "drivers": {
        "id": Long,
        "name": String,
        "phone": String,
        "dingUserId": String,
        "licensePlate": String
      }
    },
    "date": String,
    "isRide": String,
    "state": String,
    "signTime": String
  }
]
```

---

### 1.5 分页查询
- **接口地址**: `GET /api/reservation/queryAllpage`
- **请求参数**:
  - phone: String
  - isRide: String
  - state: String
  - date: String
  - schedulesId: Long
  - page: Integer
  - size: Integer
- **响应**:
```json
{
  "content": [
    {
      "id": Long,
      "name": String,
      "phone": String,
      "dingUserId": String,
      "departureSpot": String,
      "busSchedules": {
        "id": Long,
        "start_station": String,
        "end_station": String,
        "time": String,
        "date": String,
        "station": String,
        "drivers": {
          "id": Long,
          "name": String,
          "phone": String,
          "dingUserId": String,
          "licensePlate": String
        }
      },
      "date": String,
      "isRide": String,
      "state": String,
      "signTime": String
    }
  ],
  "totalElements": Integer,
  "totalPages": Integer,
  "page": Integer,
  "size": Integer
}
```

---

### 1.6 签到
- **接口地址**: `POST /api/reservation/sign`
- **请求体**:
```json
{
  "id": Long,
  "name": String,
  "phone": String,
  "dingUserId": String,
  "departureSpot": String,
  "busSchedules": {
    "id": Long,
    "start_station": String,
    "end_station": String,
    "time": String,
    "date": String,
    "station": String,
    "drivers": {
      "id": Long,
      "name": String,
      "phone": String,
      "dingUserId": String,
      "licensePlate": String
    }
  },
  "date": String,
  "isRide": String,
  "state": String,
  "signTime": String
}
```
- **响应**: 状态码 201，无内容

---

### 1.7 统计
- **接口地址**: `GET /api/reservation/statistics`
- **请求参数**:
  - phone: String
  - isRide: String
  - state: String
  - date: String
  - schedulesId: Long
- **响应**:
```json
{
  "total": Integer,
  "checkedIn": Integer,
  "cancelled": Integer
}
```

---

### 1.8 推送车辆信息
- **接口地址**: `GET /api/reservation/informationPush`
- **请求参数**:
  - phone: String
  - isRide: String
  - state: String
  - date: String
  - schedulesId: Long
- **响应**: 状态码 201，无内容

---

## 2. 班次管理接口

### 2.1 新增班次
- **接口地址**: `POST /api/schedules`
- **请求体**:
```json
{
  "id": Long,
  "start_station": String,
  "end_station": String,
  "time": String,
  "date": String,
  "station": String,
  "drivers": {
    "id": Long,
    "name": String,
    "phone": String,
    "dingUserId": String,
    "licensePlate": String
  }
}
```
- **响应**: 状态码 201，无内容

---

### 2.2 修改班次
- **接口地址**: `POST /api/schedules/put`
- **请求体**:
```json
{
  "id": Long,
  "start_station": String,
  "end_station": String,
  "time": String,
  "date": String,
  "station": String,
  "drivers": {
    "id": Long,
    "name": String,
    "phone": String,
    "dingUserId": String,
    "licensePlate": String
  }
}
```
- **响应**: 状态码 200，无内容

---

### 2.3 删除班次
- **接口地址**: `POST /api/schedules/del`
- **请求体**:
```json
{
  "id": Long
}
```
- **响应**: 状态码 200，无内容

---

### 2.4 查询班次列表
- **接口地址**: `GET /api/schedules`
- **请求参数**:
  - date: String
- **响应**:
```json
[
  {
    "id": Long,
    "start_station": String,
    "end_station": String,
    "time": String,
    "date": String,
    "station": String,
    "drivers": {
      "id": Long,
      "name": String,
      "phone": String,
      "dingUserId": String,
      "licensePlate": String
    }
  }
]
```
- **示例**:

```json
[
   {
    "id": 1,
    "start_station": "滨江校区",
    "end_station": "临安校区",
    "time": "06:30",
    "date": dayjs().format("YYYY-MM-DD"),
    "station": "滨江校区",
    "drivers": {
      "id": 1,
      "name": "张师傅",
      "phone": "13800138001",
      "dingUserId": "driver001",
      "licensePlate": "浙A12345"
    }
  },
  {
    "id": 2,
    "start_station": "滨江校区",
    "end_station": "临安校区",
    "time": "11:50",
    "date": dayjs().format("YYYY-MM-DD"),
    "station": "滨江校区",
    "drivers": {
      "id": 2,
      "name": "李师傅",
      "phone": "13800138002",
      "dingUserId": "driver002",
      "licensePlate": "浙A67890"
    }
  }
]
```

---

### 2.5 分页查询
- **接口地址**: `GET /api/schedules/queryAllpage`
- **请求参数**:
  - date: String
  - page: Integer
  - size: Integer
- **响应**:
```json
{
  "content": [
    {
      "id": Long,
      "start_station": String,
      "end_station": String,
      "time": String,
      "date": String,
      "station": String,
      "drivers": {
        "id": Long,
        "name": String,
        "phone": String,
        "dingUserId": String,
        "licensePlate": String
      }
    }
  ],
  "totalElements": Integer,
  "totalPages": Integer,
  "page": Integer,
  "size": Integer
}
```

---

### 2.6 安排驾驶员
- **接口地址**: `POST /api/schedules/arrangeDrivers`
- **请求体**:
```json
{
  "id": Long,
  "start_station": String,
  "end_station": String,
  "time": String,
  "date": String,
  "station": String,
  "drivers": {
    "id": Long,
    "name": String,
    "phone": String,
    "dingUserId": String,
    "licensePlate": String
  }
}
```
- **响应**: 状态码 201，无内容

---

## 3. 驾驶员管理接口

### 3.1 新增驾驶员
- **接口地址**: `POST /api/drivers`
- **请求体**:
```json
{
  "id": Long,
  "name": String,
  "phone": String,
  "dingUserId": String,
  "licensePlate": String
}
```
- **响应**: 状态码 201，无内容

---

### 3.2 修改驾驶员
- **接口地址**: `POST /api/drivers/put`
- **请求体**:
```json
{
  "id": Long,
  "name": String,
  "phone": String,
  "dingUserId": String,
  "licensePlate": String
}
```
- **响应**: 状态码 200，无内容

---

### 3.3 删除驾驶员
- **接口地址**: `POST /api/drivers/del`
- **请求体**:
```json
{
  "id": Long
}
```
- **响应**: 状态码 200，无内容

---

### 3.4 查询驾驶员列表
- **接口地址**: `GET /api/drivers`
- **请求参数**:
  - phone: String
  - date: String
- **响应**:
```json
[
  {
    "id": Long,
    "name": String,
    "phone": String,
    "dingUserId": String,
    "licensePlate": String
  }
]
```

---

### 3.5 分页查询
- **接口地址**: `GET /api/drivers/queryAllpage`
- **请求参数**:
  - phone: String
  - date: String
  - page: Integer
  - size: Integer
- **响应**:
```json
{
  "content": [
    {
      "id": Long,
      "name": String,
      "phone": String,
      "dingUserId": String,
      "licensePlate": String
    }
  ],
  "totalElements": Integer,
  "totalPages": Integer,
  "page": Integer,
  "size": Integer
}
```

---

### 3.6 查询空闲驾驶员
- **接口地址**: `GET /api/drivers/notArranged`
- **请求参数**:
  - phone: String
  - date: String
- **响应**:
```json
[
  {
    "id": Long,
    "name": String,
    "phone": String,
    "dingUserId": String,
    "licensePlate": String
  }
]
```

---

## 4. 爽约管理接口

### 4.1 新增爽约记录
- **接口地址**: `POST /api/noshowrecords`
- **请求体**:
```json
{
  "id": Long,
  "name": String,
  "phone": String,
  "num": Integer,
  "state": String,
  "date": String
}
```
- **响应**: 状态码 201，无内容

---

### 4.2 修改爽约记录
- **接口地址**: `POST /api/noshowrecords/put`
- **请求体**:
```json
{
  "id": Long,
  "name": String,
  "phone": String,
  "num": Integer,
  "state": String,
  "date": String
}
```
- **响应**: 状态码 200，无内容

---

### 4.3 删除爽约记录
- **接口地址**: `POST /api/noshowrecords/del`
- **请求体**:
```json
{
  "id": Long
}
```
- **响应**: 状态码 200，无内容

---

### 4.4 查询爽约记录列表
- **接口地址**: `GET /api/noshowrecords`
- **请求参数**:
  - phone: String
  - isRide: String
  - state: String
- **响应**:
```json
[
  {
    "id": Long,
    "name": String,
    "phone": String,
    "num": Integer,
    "state": String,
    "date": String,
    "reservationsList": [
      {
        "id": Long,
        "name": String,
        "phone": String,
        "dingUserId": String,
        "departureSpot": String,
        "busSchedules": {
          "id": Long,
          "start_station": String,
          "end_station": String,
          "time": String,
          "date": String,
          "station": String,
          "drivers": {
            "id": Long,
            "name": String,
            "phone": String,
            "dingUserId": String,
            "licensePlate": String
          }
        },
        "date": String,
        "isRide": String,
        "state": String,
        "signTime": String
      }
    ]
  }
]
```

---

### 4.5 分页查询
- **接口地址**: `GET /api/noshowrecords/queryAllpage`
- **请求参数**:
  - phone: String
  - isRide: String
  - state: String
  - page: Integer
  - size: Integer
- **响应**:
```json
{
  "content": [
    {
      "id": Long,
      "name": String,
      "phone": String,
      "num": Integer,
      "state": String,
      "date": String,
      "reservationsList": [
        {
          "id": Long,
          "name": String,
          "phone": String,
          "dingUserId": String,
          "departureSpot": String,
          "busSchedules": {
            "id": Long,
            "start_station": String,
            "end_station": String,
            "time": String,
            "date": String,
            "station": String,
            "drivers": {
              "id": Long,
              "name": String,
              "phone": String,
              "dingUserId": String,
              "licensePlate": String
            }
          },
          "date": String,
          "isRide": String,
          "state": String,
          "signTime": String
        }
      ]
    }
  ],
  "totalElements": Integer,
  "totalPages": Integer,
  "page": Integer,
  "size": Integer
}
```

---

### 4.6 我的爽约记录
- **接口地址**: `GET /api/noshowrecords/myRecords`
- **请求参数**:
  - phone: String
  - isRide: String
  - state: String
- **响应**:
```json
  {
    "id": Long,
    "name": String,
    "phone": String,
    "num": Integer,
    "state": String,
    "date": String,
    "reservationsList": [
      {
        "id": Long,
        "name": String,
        "phone": String,
        "dingUserId": String,
        "departureSpot": String,
        "busSchedules": {
          "id": Long,
          "start_station": String,
          "end_station": String,
          "time": String,
          "date": String,
          "station": String,
          "drivers": {
            "id": Long,
            "name": String,
            "phone": String,
            "dingUserId": String,
            "licensePlate": String
          }
        },
        "date": String,
        "isRide": String,
        "state": String,
        "signTime": String
      }
    ]
  }
```

---

### 4.7 统计
- **接口地址**: `GET /api/noshowrecords/statistics`
- **请求参数**:
  - phone: String
  - isRide: String
  - state: String
- **响应**:
```json
{
  "userCount": Integer,
  "restrictionNum": Integer,
  "TotalNumber": Integer,
}
```

---
