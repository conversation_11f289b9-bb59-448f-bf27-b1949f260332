# 两校区通勤车预约系统 - 数据库设计文档

## 数据库概述

本系统采用Oracle 19c作为主数据库，设计遵循第三范式，确保数据一致性和完整性。数据库包含用户管理、班车调度、预约管理、签到记录、爽约管理等核心业务表。

**用户体系说明**: 本系统不设独立用户体系，直接集成钉钉用户体系，使用手机号码作为用户唯一标识，通过钉钉API获取用户基本信息。

## 数据库配置

- **数据库版本**: Oracle 19c
- **字符集**: AL32UTF8
- **国家字符集**: AL16UTF16
- **时区**: Asia/Shanghai

## 表结构设计

### 1. 用户表 (users)

用户基础信息表，存储钉钉用户的基本信息。使用手机号码作为用户唯一标识。

```sql
-- 创建序列
CREATE SEQUENCE seq_users_id
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- 创建用户表
CREATE TABLE users (
  id NUMBER(19) NOT NULL,
  phone VARCHAR2(20) NOT NULL,
  name VARCHAR2(50) NOT NULL,
  user_type VARCHAR2(20) NOT NULL CHECK (user_type IN ('teacher','student')),
  department VARCHAR2(100) NOT NULL,
  dingtalk_user_id VARCHAR2(64),
  dingtalk_union_id VARCHAR2(64),
  avatar VARCHAR2(500),
  status NUMBER(1) DEFAULT 1 NOT NULL CHECK (status IN (0,1)),
  is_restricted NUMBER(1) DEFAULT 0 NOT NULL CHECK (is_restricted IN (0,1)),
  restriction_start_time DATE,
  restriction_end_time DATE,
  restriction_reason VARCHAR2(500),
  create_time DATE DEFAULT SYSDATE NOT NULL,
  update_time DATE DEFAULT SYSDATE NOT NULL,
  CONSTRAINT pk_users PRIMARY KEY (id),
  CONSTRAINT uk_users_phone UNIQUE (phone),
  CONSTRAINT uk_users_dingtalk_user_id UNIQUE (dingtalk_user_id)
);

-- 创建索引
CREATE INDEX idx_users_user_type ON users(user_type);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_department ON users(department);

-- 创建触发器自动设置ID和更新时间
CREATE OR REPLACE TRIGGER trg_users_bi
BEFORE INSERT ON users
FOR EACH ROW
BEGIN
  IF :NEW.id IS NULL THEN
    SELECT seq_users_id.NEXTVAL INTO :NEW.id FROM dual;
  END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_users_bu
BEFORE UPDATE ON users
FOR EACH ROW
BEGIN
  :NEW.update_time := SYSDATE;
END;
/

-- 添加表注释
COMMENT ON TABLE users IS '用户表-钉钉用户信息';
COMMENT ON COLUMN users.id IS '用户ID';
COMMENT ON COLUMN users.phone IS '手机号码-用户唯一标识';
COMMENT ON COLUMN users.name IS '用户姓名';
COMMENT ON COLUMN users.user_type IS '用户类型：teacher-教师，student-学生';
COMMENT ON COLUMN users.department IS '部门/学院';
COMMENT ON COLUMN users.dingtalk_user_id IS '钉钉用户ID';
COMMENT ON COLUMN users.dingtalk_union_id IS '钉钉UnionID';
COMMENT ON COLUMN users.avatar IS '头像URL';
COMMENT ON COLUMN users.status IS '状态：1-正常，0-禁用';
COMMENT ON COLUMN users.is_restricted IS '是否被限制预约：1-是，0-否';
COMMENT ON COLUMN users.restriction_start_time IS '限制开始时间';
COMMENT ON COLUMN users.restriction_end_time IS '限制结束时间';
COMMENT ON COLUMN users.restriction_reason IS '限制原因';
COMMENT ON COLUMN users.create_time IS '创建时间';
COMMENT ON COLUMN users.update_time IS '更新时间';
```

### 2. 班车时刻表 (bus_schedules)

班车时刻表配置，包含不同时期的班车安排。

```sql
-- 创建序列
CREATE SEQUENCE seq_bus_schedules_id
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- 创建班车时刻表
CREATE TABLE bus_schedules (
  id NUMBER(19) NOT NULL,
  bus_id VARCHAR2(32) NOT NULL,
  schedule_type VARCHAR2(20) NOT NULL CHECK (schedule_type IN ('weekday','weekend','vacation')),
  bus_time VARCHAR2(8) NOT NULL,
  route VARCHAR2(100) NOT NULL,
  start_station VARCHAR2(50) NOT NULL,
  end_station VARCHAR2(50) NOT NULL,
  stations CLOB NOT NULL,
  capacity NUMBER(10) DEFAULT 45 NOT NULL,
  driver_name VARCHAR2(50),
  driver_phone VARCHAR2(20),
  vehicle_number VARCHAR2(20),
  is_active NUMBER(1) DEFAULT 1 NOT NULL CHECK (is_active IN (0,1)),
  effective_date DATE,
  expiry_date DATE,
  create_time DATE DEFAULT SYSDATE NOT NULL,
  update_time DATE DEFAULT SYSDATE NOT NULL,
  CONSTRAINT pk_bus_schedules PRIMARY KEY (id),
  CONSTRAINT uk_bus_schedule UNIQUE (bus_id, schedule_type)
);

-- 创建索引
CREATE INDEX idx_bus_schedules_type ON bus_schedules(schedule_type);
CREATE INDEX idx_bus_schedules_time ON bus_schedules(bus_time);
CREATE INDEX idx_bus_schedules_active ON bus_schedules(is_active);
CREATE INDEX idx_bus_schedules_route ON bus_schedules(route);

-- 创建触发器
CREATE OR REPLACE TRIGGER trg_bus_schedules_bi
BEFORE INSERT ON bus_schedules
FOR EACH ROW
BEGIN
  IF :NEW.id IS NULL THEN
    SELECT seq_bus_schedules_id.NEXTVAL INTO :NEW.id FROM dual;
  END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_bus_schedules_bu
BEFORE UPDATE ON bus_schedules
FOR EACH ROW
BEGIN
  :NEW.update_time := SYSDATE;
END;
/

-- 添加表注释
COMMENT ON TABLE bus_schedules IS '班车时刻表';
COMMENT ON COLUMN bus_schedules.id IS '时刻表ID';
COMMENT ON COLUMN bus_schedules.bus_id IS '班车标识';
COMMENT ON COLUMN bus_schedules.schedule_type IS '时刻表类型：weekday-工作日，weekend-双休日，vacation-寒暑假';
COMMENT ON COLUMN bus_schedules.bus_time IS '发车时间(HH24:MI格式)';
COMMENT ON COLUMN bus_schedules.route IS '路线名称';
COMMENT ON COLUMN bus_schedules.start_station IS '起始站';
COMMENT ON COLUMN bus_schedules.end_station IS '终点站';
COMMENT ON COLUMN bus_schedules.stations IS '途经站点JSON数组';
COMMENT ON COLUMN bus_schedules.capacity IS '载客量';
COMMENT ON COLUMN bus_schedules.driver_name IS '司机姓名';
COMMENT ON COLUMN bus_schedules.driver_phone IS '司机电话';
COMMENT ON COLUMN bus_schedules.vehicle_number IS '车牌号';
COMMENT ON COLUMN bus_schedules.is_active IS '是否启用：1-启用，0-停用';
COMMENT ON COLUMN bus_schedules.effective_date IS '生效日期';
COMMENT ON COLUMN bus_schedules.expiry_date IS '失效日期';
COMMENT ON COLUMN bus_schedules.create_time IS '创建时间';
COMMENT ON COLUMN bus_schedules.update_time IS '更新时间';
```

### 3. 预约记录表 (reservations)

用户预约记录表，存储所有预约信息。

```sql
-- 创建序列
CREATE SEQUENCE seq_reservations_id
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- 创建预约记录表
CREATE TABLE reservations (
  id NUMBER(19) NOT NULL,
  reservation_id VARCHAR2(64) NOT NULL,
  user_phone VARCHAR2(20) NOT NULL,
  bus_id VARCHAR2(32) NOT NULL,
  reservation_date DATE NOT NULL,
  bus_time VARCHAR2(8) NOT NULL,
  bus_route VARCHAR2(100) NOT NULL,
  station VARCHAR2(50) NOT NULL,
  phone VARCHAR2(20) NOT NULL,
  remark VARCHAR2(500),
  status VARCHAR2(20) DEFAULT 'confirmed' NOT NULL CHECK (status IN ('confirmed','completed','cancelled')),
  cancel_reason VARCHAR2(500),
  cancel_time DATE,
  create_time DATE DEFAULT SYSDATE NOT NULL,
  update_time DATE DEFAULT SYSDATE NOT NULL,
  CONSTRAINT pk_reservations PRIMARY KEY (id),
  CONSTRAINT uk_reservations_id UNIQUE (reservation_id),
  CONSTRAINT fk_reservations_user_phone FOREIGN KEY (user_phone) REFERENCES users(phone) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_reservations_user_phone ON reservations(user_phone);
CREATE INDEX idx_reservations_bus_date ON reservations(bus_id, reservation_date);
CREATE INDEX idx_reservations_date_status ON reservations(reservation_date, status);
CREATE INDEX idx_reservations_create_time ON reservations(create_time);
CREATE INDEX idx_reservations_status ON reservations(status);

-- 创建触发器
CREATE OR REPLACE TRIGGER trg_reservations_bi
BEFORE INSERT ON reservations
FOR EACH ROW
BEGIN
  IF :NEW.id IS NULL THEN
    SELECT seq_reservations_id.NEXTVAL INTO :NEW.id FROM dual;
  END IF;
  IF :NEW.reservation_id IS NULL THEN
    :NEW.reservation_id := 'RSV' || TO_CHAR(SYSDATE, 'YYYYMMDD') || LPAD(seq_reservations_id.CURRVAL, 8, '0');
  END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_reservations_bu
BEFORE UPDATE ON reservations
FOR EACH ROW
BEGIN
  :NEW.update_time := SYSDATE;
END;
/

-- 添加表注释
COMMENT ON TABLE reservations IS '预约记录表';
COMMENT ON COLUMN reservations.id IS '预约ID';
COMMENT ON COLUMN reservations.reservation_id IS '预约唯一标识';
COMMENT ON COLUMN reservations.user_phone IS '用户手机号码';
COMMENT ON COLUMN reservations.bus_id IS '班车ID';
COMMENT ON COLUMN reservations.reservation_date IS '乘车日期';
COMMENT ON COLUMN reservations.bus_time IS '班车时间';
COMMENT ON COLUMN reservations.bus_route IS '班车路线';
COMMENT ON COLUMN reservations.station IS '上车站点';
COMMENT ON COLUMN reservations.phone IS '联系电话';
COMMENT ON COLUMN reservations.remark IS '备注信息';
COMMENT ON COLUMN reservations.status IS '预约状态：confirmed-已确认，completed-已完成，cancelled-已取消';
COMMENT ON COLUMN reservations.cancel_reason IS '取消原因';
COMMENT ON COLUMN reservations.cancel_time IS '取消时间';
COMMENT ON COLUMN reservations.create_time IS '创建时间';
COMMENT ON COLUMN reservations.update_time IS '更新时间';
```

### 4. 签到记录表 (check_in_records)

用户签到记录表，记录实际乘车签到信息。

```sql
-- 创建序列
CREATE SEQUENCE seq_check_in_records_id
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- 创建签到记录表
CREATE TABLE check_in_records (
  id NUMBER(19) NOT NULL,
  reservation_id VARCHAR2(64) NOT NULL,
  user_phone VARCHAR2(20) NOT NULL,
  check_in_time DATE NOT NULL,
  check_in_type VARCHAR2(20) NOT NULL CHECK (check_in_type IN ('manual','qr_scan')),
  qr_code VARCHAR2(500),
  location_latitude NUMBER(10,7),
  location_longitude NUMBER(10,7),
  location_address VARCHAR2(500),
  device_info CLOB,
  ip_address VARCHAR2(45),
  create_time DATE DEFAULT SYSDATE NOT NULL,
  CONSTRAINT pk_check_in_records PRIMARY KEY (id),
  CONSTRAINT uk_check_in_reservation_id UNIQUE (reservation_id),
  CONSTRAINT fk_checkin_reservation_id FOREIGN KEY (reservation_id) REFERENCES reservations(reservation_id) ON DELETE CASCADE,
  CONSTRAINT fk_checkin_user_phone FOREIGN KEY (user_phone) REFERENCES users(phone) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_checkin_user_phone ON check_in_records(user_phone);
CREATE INDEX idx_checkin_time ON check_in_records(check_in_time);
CREATE INDEX idx_checkin_type ON check_in_records(check_in_type);

-- 创建触发器
CREATE OR REPLACE TRIGGER trg_check_in_records_bi
BEFORE INSERT ON check_in_records
FOR EACH ROW
BEGIN
  IF :NEW.id IS NULL THEN
    SELECT seq_check_in_records_id.NEXTVAL INTO :NEW.id FROM dual;
  END IF;
END;
/

-- 添加表注释
COMMENT ON TABLE check_in_records IS '签到记录表';
COMMENT ON COLUMN check_in_records.id IS '签到ID';
COMMENT ON COLUMN check_in_records.reservation_id IS '预约ID';
COMMENT ON COLUMN check_in_records.user_phone IS '用户手机号码';
COMMENT ON COLUMN check_in_records.check_in_time IS '签到时间';
COMMENT ON COLUMN check_in_records.check_in_type IS '签到方式：manual-手动，qr_scan-扫码';
COMMENT ON COLUMN check_in_records.qr_code IS '扫描的二维码内容';
COMMENT ON COLUMN check_in_records.location_latitude IS '签到位置纬度';
COMMENT ON COLUMN check_in_records.location_longitude IS '签到位置经度';
COMMENT ON COLUMN check_in_records.location_address IS '签到位置地址';
COMMENT ON COLUMN check_in_records.device_info IS '设备信息JSON';
COMMENT ON COLUMN check_in_records.ip_address IS 'IP地址';
COMMENT ON COLUMN check_in_records.create_time IS '创建时间';
```

### 5. 爽约记录表 (no_show_records)

爽约记录表，记录用户爽约行为。

```sql
-- 创建序列
CREATE SEQUENCE seq_no_show_records_id
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- 创建爽约记录表
CREATE TABLE no_show_records (
  id NUMBER(19) NOT NULL,
  record_id VARCHAR2(64) NOT NULL,
  reservation_id VARCHAR2(64) NOT NULL,
  user_phone VARCHAR2(20) NOT NULL,
  bus_id VARCHAR2(32) NOT NULL,
  no_show_date DATE NOT NULL,
  bus_time VARCHAR2(8) NOT NULL,
  bus_route VARCHAR2(100) NOT NULL,
  station VARCHAR2(50) NOT NULL,
  record_type VARCHAR2(20) DEFAULT 'auto' NOT NULL CHECK (record_type IN ('auto','manual')),
  record_reason VARCHAR2(500),
  is_appealed NUMBER(1) DEFAULT 0 NOT NULL CHECK (is_appealed IN (0,1)),
  appeal_reason VARCHAR2(1000),
  appeal_time DATE,
  appeal_status VARCHAR2(20) CHECK (appeal_status IN ('pending','approved','rejected')),
  appeal_result VARCHAR2(1000),
  handler_phone VARCHAR2(20),
  handle_time DATE,
  is_deleted NUMBER(1) DEFAULT 0 NOT NULL CHECK (is_deleted IN (0,1)),
  delete_reason VARCHAR2(500),
  create_time DATE DEFAULT SYSDATE NOT NULL,
  update_time DATE DEFAULT SYSDATE NOT NULL,
  CONSTRAINT pk_no_show_records PRIMARY KEY (id),
  CONSTRAINT uk_no_show_record_id UNIQUE (record_id),
  CONSTRAINT fk_noshow_reservation_id FOREIGN KEY (reservation_id) REFERENCES reservations(reservation_id) ON DELETE CASCADE,
  CONSTRAINT fk_noshow_user_phone FOREIGN KEY (user_phone) REFERENCES users(phone) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_noshow_reservation_id ON no_show_records(reservation_id);
CREATE INDEX idx_noshow_user_phone ON no_show_records(user_phone);
CREATE INDEX idx_noshow_date ON no_show_records(no_show_date);
CREATE INDEX idx_noshow_is_deleted ON no_show_records(is_deleted);
CREATE INDEX idx_noshow_appeal_status ON no_show_records(appeal_status);

-- 创建触发器
CREATE OR REPLACE TRIGGER trg_no_show_records_bi
BEFORE INSERT ON no_show_records
FOR EACH ROW
BEGIN
  IF :NEW.id IS NULL THEN
    SELECT seq_no_show_records_id.NEXTVAL INTO :NEW.id FROM dual;
  END IF;
  IF :NEW.record_id IS NULL THEN
    :NEW.record_id := 'NSR' || TO_CHAR(SYSDATE, 'YYYYMMDD') || LPAD(seq_no_show_records_id.CURRVAL, 8, '0');
  END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_no_show_records_bu
BEFORE UPDATE ON no_show_records
FOR EACH ROW
BEGIN
  :NEW.update_time := SYSDATE;
END;
/

-- 添加表注释
COMMENT ON TABLE no_show_records IS '爽约记录表';
COMMENT ON COLUMN no_show_records.id IS '爽约记录ID';
COMMENT ON COLUMN no_show_records.record_id IS '记录唯一标识';
COMMENT ON COLUMN no_show_records.reservation_id IS '预约ID';
COMMENT ON COLUMN no_show_records.user_phone IS '用户手机号码';
COMMENT ON COLUMN no_show_records.bus_id IS '班车ID';
COMMENT ON COLUMN no_show_records.no_show_date IS '爽约日期';
COMMENT ON COLUMN no_show_records.bus_time IS '班车时间';
COMMENT ON COLUMN no_show_records.bus_route IS '班车路线';
COMMENT ON COLUMN no_show_records.station IS '预约站点';
COMMENT ON COLUMN no_show_records.record_type IS '记录类型：auto-自动，manual-手动';
COMMENT ON COLUMN no_show_records.record_reason IS '记录原因';
COMMENT ON COLUMN no_show_records.is_appealed IS '是否已申诉：1-是，0-否';
COMMENT ON COLUMN no_show_records.appeal_reason IS '申诉原因';
COMMENT ON COLUMN no_show_records.appeal_time IS '申诉时间';
COMMENT ON COLUMN no_show_records.appeal_status IS '申诉状态：pending-待处理，approved-通过，rejected-拒绝';
COMMENT ON COLUMN no_show_records.appeal_result IS '申诉结果';
COMMENT ON COLUMN no_show_records.handler_phone IS '处理人手机号码';
COMMENT ON COLUMN no_show_records.handle_time IS '处理时间';
COMMENT ON COLUMN no_show_records.is_deleted IS '是否已删除：1-是，0-否';
COMMENT ON COLUMN no_show_records.delete_reason IS '删除原因';
COMMENT ON COLUMN no_show_records.create_time IS '创建时间';
COMMENT ON COLUMN no_show_records.update_time IS '更新时间';
```

### 6. 系统管理员表 (admins)

系统管理员表，管理后台用户权限。

```sql
-- 创建序列
CREATE SEQUENCE seq_admins_id
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- 创建系统管理员表
CREATE TABLE admins (
  id NUMBER(19) NOT NULL,
  admin_id VARCHAR2(64) NOT NULL,
  username VARCHAR2(50) NOT NULL,
  password VARCHAR2(255) NOT NULL,
  name VARCHAR2(50) NOT NULL,
  phone VARCHAR2(20),
  email VARCHAR2(100),
  role VARCHAR2(20) DEFAULT 'operator' NOT NULL CHECK (role IN ('super_admin','admin','operator')),
  permissions CLOB,
  status NUMBER(1) DEFAULT 1 NOT NULL CHECK (status IN (0,1)),
  last_login_time DATE,
  last_login_ip VARCHAR2(45),
  create_time DATE DEFAULT SYSDATE NOT NULL,
  update_time DATE DEFAULT SYSDATE NOT NULL,
  CONSTRAINT pk_admins PRIMARY KEY (id),
  CONSTRAINT uk_admins_admin_id UNIQUE (admin_id),
  CONSTRAINT uk_admins_username UNIQUE (username)
);

-- 创建索引
CREATE INDEX idx_admins_role ON admins(role);
CREATE INDEX idx_admins_status ON admins(status);
CREATE INDEX idx_admins_phone ON admins(phone);

-- 创建触发器
CREATE OR REPLACE TRIGGER trg_admins_bi
BEFORE INSERT ON admins
FOR EACH ROW
BEGIN
  IF :NEW.id IS NULL THEN
    SELECT seq_admins_id.NEXTVAL INTO :NEW.id FROM dual;
  END IF;
  IF :NEW.admin_id IS NULL THEN
    :NEW.admin_id := 'ADM' || TO_CHAR(SYSDATE, 'YYYYMMDD') || LPAD(seq_admins_id.CURRVAL, 6, '0');
  END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_admins_bu
BEFORE UPDATE ON admins
FOR EACH ROW
BEGIN
  :NEW.update_time := SYSDATE;
END;
/

-- 添加表注释
COMMENT ON TABLE admins IS '系统管理员表';
COMMENT ON COLUMN admins.id IS '管理员ID';
COMMENT ON COLUMN admins.admin_id IS '管理员唯一标识';
COMMENT ON COLUMN admins.username IS '用户名';
COMMENT ON COLUMN admins.password IS '密码（加密）';
COMMENT ON COLUMN admins.name IS '真实姓名';
COMMENT ON COLUMN admins.phone IS '手机号码';
COMMENT ON COLUMN admins.email IS '邮箱';
COMMENT ON COLUMN admins.role IS '角色：super_admin-超级管理员，admin-管理员，operator-操作员';
COMMENT ON COLUMN admins.permissions IS '权限列表JSON';
COMMENT ON COLUMN admins.status IS '状态：1-正常，0-禁用';
COMMENT ON COLUMN admins.last_login_time IS '最后登录时间';
COMMENT ON COLUMN admins.last_login_ip IS '最后登录IP';
COMMENT ON COLUMN admins.create_time IS '创建时间';
COMMENT ON COLUMN admins.update_time IS '更新时间';
```

### 7. 系统配置表 (system_configs)

系统配置参数表，存储系统运行参数。

```sql
-- 创建序列
CREATE SEQUENCE seq_system_configs_id
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- 创建系统配置表
CREATE TABLE system_configs (
  id NUMBER(19) NOT NULL,
  config_key VARCHAR2(100) NOT NULL,
  config_value CLOB NOT NULL,
  config_type VARCHAR2(20) DEFAULT 'string' NOT NULL CHECK (config_type IN ('string','number','boolean','json')),
  description VARCHAR2(500),
  is_system NUMBER(1) DEFAULT 0 NOT NULL CHECK (is_system IN (0,1)),
  create_time DATE DEFAULT SYSDATE NOT NULL,
  update_time DATE DEFAULT SYSDATE NOT NULL,
  CONSTRAINT pk_system_configs PRIMARY KEY (id),
  CONSTRAINT uk_system_configs_key UNIQUE (config_key)
);

-- 创建索引
CREATE INDEX idx_system_configs_type ON system_configs(config_type);
CREATE INDEX idx_system_configs_system ON system_configs(is_system);

-- 创建触发器
CREATE OR REPLACE TRIGGER trg_system_configs_bi
BEFORE INSERT ON system_configs
FOR EACH ROW
BEGIN
  IF :NEW.id IS NULL THEN
    SELECT seq_system_configs_id.NEXTVAL INTO :NEW.id FROM dual;
  END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_system_configs_bu
BEFORE UPDATE ON system_configs
FOR EACH ROW
BEGIN
  :NEW.update_time := SYSDATE;
END;
/

-- 添加表注释
COMMENT ON TABLE system_configs IS '系统配置表';
COMMENT ON COLUMN system_configs.id IS '配置ID';
COMMENT ON COLUMN system_configs.config_key IS '配置键';
COMMENT ON COLUMN system_configs.config_value IS '配置值';
COMMENT ON COLUMN system_configs.config_type IS '配置类型：string-字符串，number-数字，boolean-布尔，json-JSON';
COMMENT ON COLUMN system_configs.description IS '配置描述';
COMMENT ON COLUMN system_configs.is_system IS '是否系统配置：1-是，0-否';
COMMENT ON COLUMN system_configs.create_time IS '创建时间';
COMMENT ON COLUMN system_configs.update_time IS '更新时间';
```

### 8. 操作日志表 (operation_logs)

系统操作日志表，记录重要操作。

```sql
-- 创建序列
CREATE SEQUENCE seq_operation_logs_id
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- 创建操作日志表
CREATE TABLE operation_logs (
  id NUMBER(19) NOT NULL,
  log_id VARCHAR2(64) NOT NULL,
  operator_id VARCHAR2(64) NOT NULL,
  operator_type VARCHAR2(20) NOT NULL CHECK (operator_type IN ('user','admin')),
  operation_type VARCHAR2(50) NOT NULL,
  operation_desc VARCHAR2(500) NOT NULL,
  target_type VARCHAR2(50),
  target_id VARCHAR2(64),
  request_data CLOB,
  response_data CLOB,
  ip_address VARCHAR2(45),
  user_agent VARCHAR2(1000),
  status VARCHAR2(20) NOT NULL CHECK (status IN ('success','failure')),
  error_message VARCHAR2(1000),
  execution_time NUMBER(10),
  create_time DATE DEFAULT SYSDATE NOT NULL,
  CONSTRAINT pk_operation_logs PRIMARY KEY (id),
  CONSTRAINT uk_operation_logs_log_id UNIQUE (log_id)
);

-- 创建索引
CREATE INDEX idx_operation_logs_operator ON operation_logs(operator_id, operator_type);
CREATE INDEX idx_operation_logs_type ON operation_logs(operation_type);
CREATE INDEX idx_operation_logs_create_time ON operation_logs(create_time);
CREATE INDEX idx_operation_logs_status ON operation_logs(status);
CREATE INDEX idx_operation_logs_target ON operation_logs(target_type, target_id);

-- 创建触发器
CREATE OR REPLACE TRIGGER trg_operation_logs_bi
BEFORE INSERT ON operation_logs
FOR EACH ROW
BEGIN
  IF :NEW.id IS NULL THEN
    SELECT seq_operation_logs_id.NEXTVAL INTO :NEW.id FROM dual;
  END IF;
  IF :NEW.log_id IS NULL THEN
    :NEW.log_id := 'LOG' || TO_CHAR(SYSDATE, 'YYYYMMDDHH24MISS') || LPAD(seq_operation_logs_id.CURRVAL, 6, '0');
  END IF;
END;
/

-- 添加表注释
COMMENT ON TABLE operation_logs IS '操作日志表';
COMMENT ON COLUMN operation_logs.id IS '日志ID';
COMMENT ON COLUMN operation_logs.log_id IS '日志唯一标识';
COMMENT ON COLUMN operation_logs.operator_id IS '操作者ID';
COMMENT ON COLUMN operation_logs.operator_type IS '操作者类型：user-用户，admin-管理员';
COMMENT ON COLUMN operation_logs.operation_type IS '操作类型';
COMMENT ON COLUMN operation_logs.operation_desc IS '操作描述';
COMMENT ON COLUMN operation_logs.target_type IS '操作对象类型';
COMMENT ON COLUMN operation_logs.target_id IS '操作对象ID';
COMMENT ON COLUMN operation_logs.request_data IS '请求数据JSON';
COMMENT ON COLUMN operation_logs.response_data IS '响应数据JSON';
COMMENT ON COLUMN operation_logs.ip_address IS 'IP地址';
COMMENT ON COLUMN operation_logs.user_agent IS '用户代理';
COMMENT ON COLUMN operation_logs.status IS '操作状态：success-成功，failure-失败';
COMMENT ON COLUMN operation_logs.error_message IS '错误信息';
COMMENT ON COLUMN operation_logs.execution_time IS '执行时间（毫秒）';
COMMENT ON COLUMN operation_logs.create_time IS '创建时间';
```

### 9. 消息通知表 (notifications)

消息通知表，存储系统通知信息。

```sql
-- 创建序列
CREATE SEQUENCE seq_notifications_id
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- 创建消息通知表
CREATE TABLE notifications (
  id NUMBER(19) NOT NULL,
  notification_id VARCHAR2(64) NOT NULL,
  user_phone VARCHAR2(20) NOT NULL,
  notification_type VARCHAR2(50) NOT NULL,
  title VARCHAR2(200) NOT NULL,
  content CLOB NOT NULL,
  data CLOB,
  channel VARCHAR2(20) DEFAULT 'system' NOT NULL CHECK (channel IN ('system','dingtalk','sms','email')),
  status VARCHAR2(20) DEFAULT 'pending' NOT NULL CHECK (status IN ('pending','sent','failed','read')),
  send_time DATE,
  read_time DATE,
  retry_count NUMBER(10) DEFAULT 0 NOT NULL,
  error_message VARCHAR2(1000),
  create_time DATE DEFAULT SYSDATE NOT NULL,
  update_time DATE DEFAULT SYSDATE NOT NULL,
  CONSTRAINT pk_notifications PRIMARY KEY (id),
  CONSTRAINT uk_notifications_id UNIQUE (notification_id),
  CONSTRAINT fk_notifications_user_phone FOREIGN KEY (user_phone) REFERENCES users(phone) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_notifications_user_phone ON notifications(user_phone);
CREATE INDEX idx_notifications_type ON notifications(notification_type);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_create_time ON notifications(create_time);
CREATE INDEX idx_notifications_channel ON notifications(channel);

-- 创建触发器
CREATE OR REPLACE TRIGGER trg_notifications_bi
BEFORE INSERT ON notifications
FOR EACH ROW
BEGIN
  IF :NEW.id IS NULL THEN
    SELECT seq_notifications_id.NEXTVAL INTO :NEW.id FROM dual;
  END IF;
  IF :NEW.notification_id IS NULL THEN
    :NEW.notification_id := 'NTF' || TO_CHAR(SYSDATE, 'YYYYMMDDHH24MISS') || LPAD(seq_notifications_id.CURRVAL, 6, '0');
  END IF;
END;
/

CREATE OR REPLACE TRIGGER trg_notifications_bu
BEFORE UPDATE ON notifications
FOR EACH ROW
BEGIN
  :NEW.update_time := SYSDATE;
END;
/

-- 添加表注释
COMMENT ON TABLE notifications IS '消息通知表';
COMMENT ON COLUMN notifications.id IS '通知ID';
COMMENT ON COLUMN notifications.notification_id IS '通知唯一标识';
COMMENT ON COLUMN notifications.user_phone IS '接收用户手机号码';
COMMENT ON COLUMN notifications.notification_type IS '通知类型';
COMMENT ON COLUMN notifications.title IS '通知标题';
COMMENT ON COLUMN notifications.content IS '通知内容';
COMMENT ON COLUMN notifications.data IS '附加数据JSON';
COMMENT ON COLUMN notifications.channel IS '通知渠道：system-系统，dingtalk-钉钉，sms-短信，email-邮件';
COMMENT ON COLUMN notifications.status IS '通知状态：pending-待发送，sent-已发送，failed-发送失败，read-已阅读';
COMMENT ON COLUMN notifications.send_time IS '发送时间';
COMMENT ON COLUMN notifications.read_time IS '阅读时间';
COMMENT ON COLUMN notifications.retry_count IS '重试次数';
COMMENT ON COLUMN notifications.error_message IS '错误信息';
COMMENT ON COLUMN notifications.create_time IS '创建时间';
COMMENT ON COLUMN notifications.update_time IS '更新时间';
```

## 索引设计说明

### 主要索引策略

1. **主键索引**: 所有表都使用NUMBER类型自增ID作为主键，通过序列和触发器实现
2. **唯一索引**: 业务唯一标识字段建立唯一约束索引
3. **外键索引**: 外键字段建立索引提升关联查询性能
4. **查询索引**: 根据常用查询条件建立复合索引
5. **时间索引**: DATE类型时间字段建立索引支持时间范围查询

### Oracle性能优化建议

1. **分区策略**: 大表可按时间分区（RANGE分区按月/年）或按业务分区（LIST分区）
2. **读写分离**: 使用Oracle Data Guard配置主备库实现读写分离
3. **缓存策略**: 热点数据使用Redis缓存，Oracle SGA适当调优
4. **归档策略**: 历史数据定期归档，使用Oracle分区交换技术
5. **统计信息**: 定期收集表和索引统计信息，保证CBO优化器准确性

## 数据字典

### 枚举值说明

#### 用户类型 (user_type)
- `teacher`: 教师
- `student`: 学生

#### 预约状态 (reservation_status)
- `confirmed`: 已确认
- `completed`: 已完成
- `cancelled`: 已取消

#### 签到方式 (check_in_type)
- `manual`: 手动签到
- `qr_scan`: 扫码签到

#### 时刻表类型 (schedule_type)
- `weekday`: 工作日
- `weekend`: 双休日
- `vacation`: 寒暑假

#### 通知渠道 (notification_channel)
- `system`: 系统内通知
- `dingtalk`: 钉钉通知
- `sms`: 短信通知
- `email`: 邮件通知

#### 申诉状态 (appeal_status)
- `pending`: 待处理
- `approved`: 申诉通过
- `rejected`: 申诉拒绝

## 初始化数据

### 系统配置初始化
```sql
-- 插入系统配置数据
INSERT INTO system_configs (config_key, config_value, config_type, description) VALUES
('reservation_advance_days', '2', 'number', '可预约天数');

INSERT INTO system_configs (config_key, config_value, config_type, description) VALUES
('reservation_deadline_hour', '18', 'number', '预约截止时间（小时）');

INSERT INTO system_configs (config_key, config_value, config_type, description) VALUES
('checkin_window_minutes', '30', 'number', '签到时间窗口（分钟）');

INSERT INTO system_configs (config_key, config_value, config_type, description) VALUES
('noshow_limit_count', '3', 'number', '爽约限制次数');

INSERT INTO system_configs (config_key, config_value, config_type, description) VALUES
('restriction_duration_days', '7', 'number', '限制持续天数');

INSERT INTO system_configs (config_key, config_value, config_type, description) VALUES
('bus_capacity_default', '45', 'number', '默认班车载客量');

INSERT INTO system_configs (config_key, config_value, config_type, description) VALUES
('dingtalk_app_key', '', 'string', '钉钉应用AppKey');

INSERT INTO system_configs (config_key, config_value, config_type, description) VALUES
('dingtalk_app_secret', '', 'string', '钉钉应用AppSecret');

-- 提交事务
COMMIT;
```

### 班车时刻表初始化
```sql
-- 工作日班次数据插入示例
INSERT INTO bus_schedules (bus_id, schedule_type, bus_time, route, start_station, end_station, stations, capacity) VALUES
('WD001', 'weekday', '06:30', '滨江至临安', '滨江校区', '青山湖地铁口', '["滨江校区", "汽车西站", "五常站", "青山湖地铁口"]', 45);

INSERT INTO bus_schedules (bus_id, schedule_type, bus_time, route, start_station, end_station, stations, capacity) VALUES
('WD002', 'weekday', '11:50', '滨江至临安', '滨江校区', '青山湖地铁口', '["滨江校区", "汽车西站", "五常站", "青山湖地铁口"]', 45);

INSERT INTO bus_schedules (bus_id, schedule_type, bus_time, route, start_station, end_station, stations, capacity) VALUES
('WD003', 'weekday', '17:30', '临安至滨江', '青山湖地铁口', '滨江校区', '["青山湖地铁口", "五常站", "汽车西站", "滨江校区"]', 45);

-- 周末班次数据
INSERT INTO bus_schedules (bus_id, schedule_type, bus_time, route, start_station, end_station, stations, capacity) VALUES
('WE001', 'weekend', '08:00', '滨江至临安', '滨江校区', '青山湖地铁口', '["滨江校区", "汽车西站", "五常站", "青山湖地铁口"]', 45);

INSERT INTO bus_schedules (bus_id, schedule_type, bus_time, route, start_station, end_station, stations, capacity) VALUES
('WE002', 'weekend', '16:00', '临安至滨江', '青山湖地铁口', '滨江校区', '["青山湖地铁口", "五常站", "汽车西站", "滨江校区"]', 45);

-- 提交事务
COMMIT;
```

### 管理员账户初始化
```sql
-- 创建超级管理员账户（密码：admin123，需要加密存储）
INSERT INTO admins (username, password, name, phone, email, role, status) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyVqNhKwvKWyeWv0U7dDj0/Hn5G', '系统管理员', '13800138000', '<EMAIL>', 'super_admin', 1);

-- 提交事务
COMMIT;
```

## 备份与恢复策略

### Oracle备份策略
1. **RMAN全量备份**: 每日凌晨进行数据库全量备份
2. **RMAN增量备份**: 每小时进行增量备份（Level 1）
3. **归档日志备份**: 实时备份归档日志文件
4. **异地备份**: 重要数据通过Data Guard异地存储
5. **闪回区配置**: 配置闪回恢复区支持快速恢复

### Oracle恢复策略
1. **时间点恢复**: 使用RMAN支持任意时间点数据恢复（PITR）
2. **表级恢复**: 使用闪回表或RMAN辅助实例恢复单表
3. **闪回恢复**: 利用闪回技术快速恢复误操作
4. **Data Guard切换**: 主备库角色切换实现灾难恢复

### 备份脚本示例
```bash
#!/bin/bash
# RMAN备份脚本
export ORACLE_SID=BUSDB
export ORACLE_HOME=/u01/app/oracle/product/19.0.0/dbhome_1
export PATH=$ORACLE_HOME/bin:$PATH

rman target / <<EOF
CONFIGURE RETENTION POLICY TO RECOVERY WINDOW OF 30 DAYS;
CONFIGURE BACKUP OPTIMIZATION ON;
CONFIGURE DEFAULT DEVICE TYPE TO DISK;
CONFIGURE CONTROLFILE AUTOBACKUP ON;

BACKUP DATABASE PLUS ARCHIVELOG;
DELETE NOPROMPT OBSOLETE;
EXIT;
EOF
```

## 数据安全

### Oracle数据安全策略

#### 敏感数据处理
1. **密码加密**: 使用bcrypt加密存储管理员密码
2. **手机号脱敏**: 日志和报表中手机号脱敏显示（如：138****8000）
3. **数据权限**: 使用Oracle VPD（虚拟专用数据库）控制数据访问权限
4. **审计日志**: 启用Oracle统一审计记录所有敏感操作
5. **钉钉集成安全**: 钉钉用户信息加密传输和存储

#### Oracle安全配置
```sql
-- 启用统一审计
ALTER SYSTEM SET AUDIT_TRAIL=DB,EXTENDED SCOPE=SPFILE;

-- 创建审计策略
CREATE AUDIT POLICY sensitive_data_policy
ACTIONS SELECT, INSERT, UPDATE, DELETE
ON users, reservations, no_show_records;

-- 启用审计策略
AUDIT POLICY sensitive_data_policy;

-- 创建数据脱敏函数
CREATE OR REPLACE FUNCTION mask_phone(p_phone VARCHAR2) RETURN VARCHAR2 IS
BEGIN
  IF LENGTH(p_phone) = 11 THEN
    RETURN SUBSTR(p_phone, 1, 3) || '****' || SUBSTR(p_phone, 8, 4);
  ELSE
    RETURN p_phone;
  END IF;
END;
/
```

### 数据完整性保障
1. **外键约束**: 确保数据引用完整性，防止孤立数据
2. **检查约束**: 添加业务规则检查约束，确保数据有效性
3. **事务控制**: 关键操作使用事务保证ACID特性
4. **触发器验证**: 使用触发器进行复杂业务规则验证
5. **序列保证**: 使用Oracle序列保证ID唯一性

### 钉钉集成安全要求
1. **API密钥管理**: 钉钉AppKey和AppSecret加密存储
2. **访问令牌**: 定期刷新钉钉访问令牌
3. **数据同步**: 定期同步钉钉用户信息，保持数据一致性
4. **权限验证**: 通过钉钉API验证用户身份和权限

### 数据库连接安全
```sql
-- 创建应用专用用户
CREATE USER bus_app IDENTIFIED BY "ComplexPassword123!";

-- 授予必要权限
GRANT CONNECT, RESOURCE TO bus_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON users TO bus_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON reservations TO bus_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON bus_schedules TO bus_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON check_in_records TO bus_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON no_show_records TO bus_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON notifications TO bus_app;
GRANT SELECT, INSERT, UPDATE ON system_configs TO bus_app;
GRANT INSERT ON operation_logs TO bus_app;

-- 限制管理员表访问
-- 管理员表只允许管理后台访问，不授权给应用用户
```

## 总结

本数据库设计文档已完全适配Oracle数据库，主要特点：

1. **用户体系**: 采用钉钉用户体系，手机号码作为用户唯一标识
2. **数据类型**: 使用Oracle标准数据类型（NUMBER、VARCHAR2、DATE、CLOB等）
3. **约束机制**: 使用CHECK约束替代MySQL的ENUM类型
4. **自增主键**: 通过序列（SEQUENCE）和触发器（TRIGGER）实现
5. **JSON支持**: 使用CLOB存储JSON数据
6. **安全机制**: 集成Oracle安全特性和钉钉认证体系
7. **性能优化**: 针对Oracle特性进行索引和分区优化

该设计确保了系统的安全性、可靠性和高性能，满足两校区通勤车预约系统的业务需求。
