# 两校区通勤车预约系统 - 文档中心

## 文档概述

本文档中心包含了两校区通勤车预约系统的完整技术文档，涵盖功能规格、接口设计、数据库结构等核心内容。

## 文档目录

### 📋 [功能清单](./功能清单.md)
- **文档描述**: 系统完整功能清单和模块说明
- **主要内容**:
  - 核心功能模块详细说明
  - 用户角色和权限设计
  - 业务流程和规则说明
  - 技术架构和特色功能
  - 后续扩展计划

### 🔌 [接口文档](./接口文档.md)
- **文档描述**: 系统后端API接口完整规范
- **主要内容**:
  - RESTful API接口设计
  - 请求响应格式规范
  - 接口参数和返回值说明
  - 错误码和异常处理
  - 接口调用示例

### 🗄️ [数据库设计](./数据库设计.md)
- **文档描述**: 数据库表结构和设计规范
- **主要内容**:
  - 完整的表结构设计
  - 索引和约束设计
  - 数据字典和枚举说明
  - 性能优化建议
  - 备份恢复策略

## 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用层     │    │   后端服务层     │    │   数据存储层     │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ Vue 3 + Vant    │    │ Spring Boot     │    │ MySQL 8.0       │
│ 移动端H5应用     │◄──►│ RESTful API     │◄──►│ 主数据库         │
│ 钉钉集成        │    │ JWT认证         │    │                 │
└─────────────────┘    │ 业务逻辑处理     │    ├─────────────────┤
                       └─────────────────┘    │ Redis           │
                                             │ 缓存数据库       │
                                             └─────────────────┘
```

## 核心业务流程

### 预约流程
```mermaid
graph TD
    A[用户登录] --> B[选择日期]
    B --> C[选择班车]
    C --> D[选择站点]
    D --> E[填写信息]
    E --> F[提交预约]
    F --> G[预约成功]
    G --> H[等待乘车]
    H --> I[签到确认]
    I --> J[完成乘车]
```

### 爽约管理流程
```mermaid
graph TD
    A[预约成功] --> B[班车发车]
    B --> C{是否签到?}
    C -->|是| D[完成乘车]
    C -->|否| E[记录爽约]
    E --> F{爽约次数?}
    F -->|<3次| G[发送警告]
    F -->|≥3次| H[限制预约]
    H --> I[等待解除]
    I --> J[恢复权限]
```

## 技术栈说明

### 前端技术
- **Vue 3**: 渐进式JavaScript框架
- **Vant 4**: 移动端Vue组件库
- **Vue Router 4**: 官方路由管理器
- **Pinia**: 状态管理库
- **Vite**: 现代化构建工具
- **Day.js**: 轻量级日期处理库

### 后端技术（推荐）
- **Spring Boot**: Java企业级开发框架
- **Spring Security**: 安全认证框架
- **MyBatis Plus**: 数据访问层框架
- **Redis**: 内存数据库
- **MySQL**: 关系型数据库
- **JWT**: 无状态认证方案

### 集成技术
- **钉钉JS-SDK**: 钉钉开放平台集成
- **dingtalk-jsapi**: 钉钉前端API库
- **Nginx**: Web服务器和反向代理
- **Docker**: 容器化部署

## 开发环境搭建

### 前端环境
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

### 数据库环境
```bash
# 创建数据库
CREATE DATABASE bus_reservation CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入表结构
mysql -u root -p bus_reservation < database/schema.sql

# 导入初始数据
mysql -u root -p bus_reservation < database/init_data.sql
```

## 部署说明

### 生产环境部署
1. **前端部署**: 构建静态文件部署到CDN或Web服务器
2. **后端部署**: 打包JAR文件部署到应用服务器
3. **数据库部署**: MySQL主从配置，Redis集群部署
4. **反向代理**: Nginx配置HTTPS和负载均衡

### 钉钉集成配置
1. **创建钉钉应用**: 在钉钉开发者后台创建H5微应用
2. **配置域名**: 设置应用访问域名和IP白名单
3. **获取凭证**: 获取AppKey、AppSecret等认证信息
4. **权限配置**: 配置所需的API权限和用户权限

## 监控与运维

### 系统监控
- **应用监控**: 使用APM工具监控应用性能
- **数据库监控**: 监控数据库连接、查询性能
- **服务器监控**: 监控CPU、内存、磁盘使用情况
- **业务监控**: 监控预约量、签到率等业务指标

### 日志管理
- **应用日志**: 记录业务操作和异常信息
- **访问日志**: 记录用户访问和API调用
- **错误日志**: 记录系统错误和异常堆栈
- **审计日志**: 记录敏感操作和数据变更

## 版本管理

### 版本号规范
- **主版本号**: 重大功能更新或架构变更
- **次版本号**: 新功能添加或重要改进
- **修订版本号**: Bug修复和小幅优化

### 发布流程
1. **开发分支**: feature分支开发新功能
2. **测试分支**: develop分支集成测试
3. **预发布**: release分支预发布测试
4. **生产发布**: master分支生产环境发布

## 联系方式

### 开发团队
- **项目负责人**: [姓名] - [邮箱]
- **前端开发**: [姓名] - [邮箱]
- **后端开发**: [姓名] - [邮箱]
- **数据库设计**: [姓名] - [邮箱]

### 技术支持
- **技术咨询**: [邮箱]
- **Bug反馈**: [邮箱]
- **功能建议**: [邮箱]

## 更新日志

### v1.0.0 (2024-01-15)
- ✅ 完成基础功能开发
- ✅ 实现用户预约管理
- ✅ 集成钉钉扫码签到
- ✅ 完善爽约管理机制
- ✅ 优化移动端体验

### 计划更新
- [ ] v1.1.0: 增加数据统计分析功能
- [ ] v1.2.0: 支持班车实时位置追踪
- [ ] v2.0.0: 重构为微服务架构

---

**文档最后更新时间**: 2024-01-15  
**文档版本**: v1.0.0
