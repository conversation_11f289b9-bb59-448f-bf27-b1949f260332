# bus

# 两校区通勤车预约系统

基于Vue 3 + Vant的移动端H5应用，用于集成到钉钉端实现校车预约功能。

## 功能特性

### 🚌 用户端功能
- **用户信息管理**: 支持教师和学生身份登录，完善个人信息
- **班车预约**: 可预约后2天的通勤需求（当日18:00为截止时间）
- **时刻表查询**: 显示工作日、双休日、寒暑假不同的班车安排
- **我的预约**: 查看预约历史，支持取消预约
- **现场签到**: 支持扫码签到和手动签到确认
- **爽约管理**: 自动记录爽约次数，3次爽约限制预约权限1周

### 📊 管理端功能
- **乘客名单**: 查看每个班次的乘客信息和人数统计
- **数据导出**: 支持导出乘客名单和统计报表
- **实时监控**: 显示今日预约、签到、爽约等统计数据
- **推送通知**: 向驾驶员和乘客推送班车信息

## 班车时刻表

### 工作日班次
**滨江至临安**
- 06:30 (滨江校区 → 汽车西站 → 五常站 → 青山湖地铁口)
- 11:50 (滨江校区 → 汽车西站 → 五常站 → 青山湖地铁口)
- 16:20 (滨江校区 → 汽车西站 → 五常站 → 青山湖地铁口)

**临安至滨江**
- 12:10 (临安校区 → 青山湖地铁口 → 五常站 → 汽车西站)
- 17:00 (临安校区 → 青山湖地铁口 → 五常站 → 汽车西站)
- 21:20 (临安校区 → 青山湖地铁口 → 五常站 → 汽车西站)

### 双休日班次（仅周日）
- 16:20 滨江至临安
- 18:30 临安至滨江

### 寒暑假班次
- 07:30 滨江至临安
- 16:30 临安至滨江

## 技术栈

- **前端框架**: Vue 3 (Composition API)
- **UI组件库**: Vant 4 (移动端)
- **路由管理**: Vue Router 4
- **状态管理**: Pinia
- **HTTP请求**: Axios
- **日期处理**: Day.js
- **构建工具**: Vite
- **开发语言**: JavaScript

## 项目结构

```
src/
├── views/              # 页面组件
│   ├── Home.vue       # 首页/快速预约
│   ├── Reservation.vue # 预约详情页
│   ├── MyReservations.vue # 我的预约
│   ├── CheckIn.vue    # 签到页面
│   └── Admin.vue      # 管理后台
├── stores/            # 状态管理
│   └── bus.js         # 班车相关状态
├── router/            # 路由配置
│   └── index.js       # 路由定义
├── components/        # 公共组件
├── assets/           # 静态资源
├── App.vue           # 根组件
└── main.js           # 入口文件
```

## 开发指南

### 环境要求
- Node.js 20.19+ 或 22.12+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产构建
```bash
npm run preview
```

## 核心业务逻辑

### 预约规则
1. 只能预约后2天的班车（当日18:00为截止时间）
2. 如果当前时间超过18:00，则不能预约明天，只能预约后天开始
3. 必须选择时间、车次、站点（必填项）
4. 被限制用户无法进行预约

### 签到规则
1. 只能在班车发车前30分钟到发车后30分钟内签到
2. 支持手动签到和扫码签到两种方式
3. 签到成功后预约状态变更为"已完成"

### 爽约管理
1. 预约2次未乘坐发送提醒通知
2. 预约3次未乘坐取消预约权限1周
3. 限制时间从第三次爽约当日下午18点开始计算

## 部署说明

### 钉钉集成
1. 在钉钉开发者后台创建H5微应用
2. 配置应用首页地址为部署后的域名
3. 设置应用权限和可见范围
4. 配置钉钉JS-SDK获取用户信息

### 生产部署
1. 执行 `npm run build` 构建生产版本
2. 将 `dist` 目录部署到Web服务器
3. 配置HTTPS证书（钉钉要求）
4. 设置正确的域名和路径

## 待开发功能

- [ ] 钉钉用户身份集成
- [ ] 后端API接口对接
- [ ] 二维码扫码签到
- [ ] 消息推送功能
- [ ] Excel报表导出
- [ ] 数据统计图表
- [ ] 管理员权限控制

## 联系方式

如有问题请联系开发团队或提交Issue。

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Compile and Minify for Production

```sh
npm run build
```
